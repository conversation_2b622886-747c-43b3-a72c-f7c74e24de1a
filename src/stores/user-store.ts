import { getUserInfo } from "@/services/user";
import { createStore } from "zustand/vanilla";
export type UserState = {
  user_id: string;
  user_name: string;
  email: string;
  avatar?: string;
  isLogin: boolean;
  tokens: number;
  freeze_tokens: number;
};

export type UserActions = {
  logout: () => void;
  email_login: (user: UserState) => void;
  fetchInitUser: () => void;
};

export type UserStore = UserState & UserActions;

export const defaultInitState: UserState = {
  user_id: "",
  user_name: "",
  email: "",
  avatar: "",
  isLogin: false,
  tokens: 0,
  freeze_tokens: 0,
};

export const createUserStore = (initState: UserState = defaultInitState) => {
  return createStore<UserStore>()((set) => ({
    ...initState,
    email_login: (user: UserState) => {
      set({
        ...user,
        isLogin: true,
      });
    },
    fetchInitUser: async () => {
      // TODO:获取用户信息
      const token = localStorage.getItem("token");
      if (token) {
        let res: any = await getUserInfo();
        if (res.status === "success") {
          let user: UserState = {
            user_id: res.user.id,
            user_name: res.user.user_name,
            email: res.user.email,
            avatar: res.user.avatar,
            tokens: res.tokens,
            freeze_tokens: res.freeze_tokens,
            isLogin: true,
          };
          set(user);
        }
      }
    },
    logout: () => {
      localStorage.removeItem("token");
      set({ isLogin: false });
    },
  }));
};
