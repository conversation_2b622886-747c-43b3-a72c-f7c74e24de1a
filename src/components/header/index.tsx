"use client";
import UserCtrl from "@/components/user_ctrl";

const Index = () => {
  return (
    <>
      <div
        className="
        sticky top-0 bg-white h-24 flex items-center border-0 border-gray-200 border-b-1 px-6 text-lg
        z-20 lg:z-0
        "
      >
        <div className="flex-1 flex items-center justify-center">
          <div
            className="
            border-2 border-transparent h-12 bg-gray-200 rounded-xl flex items-center px-5 focus-within:border-[#3d86ef]
            mx-20 jusctify-center flex-1 max-w-md
          "
          >
            <input
              type="text"
              placeholder="Search or jump to..."
              className="outline-0 lg:w-md user-select-none flex-1"
            />
            <div className="text-sm">⌘K</div>
          </div>
        </div>

        {/* 用户操作 */}
        <div className="lg:flex items-center gap-6 hidden">
          <UserCtrl />
        </div>
      </div>
      <div className="shadow -z-10"></div>
    </>
  );
};

export default Index;
