import Link from "next/link";
import React from "react";
import { useUserStore } from "@/providers/user-store-provider";

const Index = () => {
  const avatar = useUserStore((state) => state.avatar);
  const user_name = useUserStore((state) => state.user_name);
  const isLogin = useUserStore((state) => state.isLogin);
  if (!isLogin) {
    return null;
  }

  return (
    <>
      {/* 消息通知 */}
      <div className="cursor-pointer text-gray-700">
        <svg
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="9709"
          width="22"
          height="22"
          fill="currentColor"
        >
          <path
            d="M816.0256 768h-24.064V428.032a279.9616 279.9616 0 0 0-239.9744-277.1456v-38.912a39.9872 39.9872 0 1 0-79.9744 0v38.912A279.9616 279.9616 0 0 0 231.936 428.032V768h-23.9616a31.9488 31.9488 0 0 0-32 32v32c0 4.4032 3.584 7.9872 7.9872 7.9872h216.0128c0 61.7984 50.176 112.0256 111.9744 112.0256 61.7984 0 112.0256-50.176 112.0256-112.0256h215.9616c4.4032 0 8.0384-3.584 8.0384-7.9872v-32a31.9488 31.9488 0 0 0-32-32zM512 888.0128a48.0256 48.0256 0 0 1-48.0256-48.0256h96.0512c0 26.5216-21.504 48.0256-48.0256 48.0256zM303.9744 768V428.032c0-55.6544 21.6064-107.8272 60.928-147.1488A206.5408 206.5408 0 0 1 512 219.9552c55.6032 0 107.776 21.6576 147.0976 60.928a206.5408 206.5408 0 0 1 60.928 147.0976V768H303.9744z"
            p-id="9710"
          ></path>
        </svg>
      </div>

      {/* 帮助 */}
      <div className="cursor-pointer text-gray-700">
        <svg
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="11325"
          width="18"
          height="18"
          fill="currentColor"
        >
          <path
            d="M512.032 0C229.216 0 0 229.216 0 512c0 282.752 229.216 512 512.032 512C794.816 1024 1024 794.752 1024 512c0-282.784-229.184-512-511.968-512z m-0.064 960C264.576 960 63.936 759.392 63.936 512c0-247.424 200.64-448 448.032-448C759.488 64 960 264.576 960 512c0 247.392-200.512 448-448.032 448z"
            p-id="11326"
          ></path>
          <path
            d="M512 208a176.192 176.192 0 0 0-176 176 48 48 0 1 0 96 0c0-44.096 35.872-80 80-80s80 35.904 80 80c0 28.8-15.36 54.688-41.056 69.248a176.768 176.768 0 0 0-88.128 152.576 48 48 0 1 0 96 0c0-28.384 15.328-54.944 39.936-69.248l2.112-1.312A176.16 176.16 0 0 0 688 384c0-97.056-78.944-176-176-176z"
            p-id="11327"
          ></path>
          <path
            d="M512 768m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
            p-id="11328"
          ></path>
        </svg>
      </div>

      {/* 个人中心 */}
      <Link href="/setting">
        {avatar ? (
          <img src={avatar} alt="avatar" className="rounded-full w-12 h-12" />
        ) : (
          <div className="rounded-full w-12 h-12 flex items-center justify-center bg-blue-500 text-white cursor-pointer">
            {user_name.slice(0, 1)}
          </div>
        )}
      </Link>
    </>
  );
};

export default Index;
