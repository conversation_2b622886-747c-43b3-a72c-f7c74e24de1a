"use client";

import { AnimatePresence, motion } from "motion/react";
import { useEffect, useState } from "react";

type IProps = {
  content: string;
  type: "success" | "error" | "warning" | "info";
};

const Colors = {
  success: "bg-green-50 border-green-200 text-green-600",
  error: "bg-red-50 border-red-200 text-red-600",
  warning: "bg-orange-50 border-orange-200 text-orange-600",
  info: "bg-blue-50 border-blue-200 text-blue-600",
};

const Icon = {
  success: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="3485"
      className="w-6 h-6"
      fill="currentColor"
    >
      <path
        d="M512 896C299.936 896 128 724.064 128 512S299.936 128 512 128s384 171.936 384 384-171.936 384-384 384m0-832C264.96 64 64 264.96 64 512s200.96 448 448 448 448-200.96 448-448S759.04 64 512 64"
        p-id="3486"
      ></path>
      <path
        d="M432 618.752l-121.376-121.376-45.248 45.248 166.624 166.624 326.624-326.624-45.248-45.248z"
        p-id="3487"
      ></path>
    </svg>
  ),
  error: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="3888"
      className="w-6 h-6"
      fill="currentColor"
    >
      <path
        d="M512 896C299.936 896 128 724.064 128 512S299.936 128 512 128s384 171.936 384 384-171.936 384-384 384m0-832C264.96 64 64 264.96 64 512s200.96 448 448 448 448-200.96 448-448S759.04 64 512 64"
        p-id="3889"
      ></path>
      <path
        d="M665.376 313.376L512 466.752l-153.376-153.376-45.248 45.248L466.752 512l-153.376 153.376 45.248 45.248L512 557.248l153.376 153.376 45.248-45.248L557.248 512l153.376-153.376z"
        p-id="3890"
      ></path>
    </svg>
  ),
  warning: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="3273"
      className="w-6 h-6"
      fill="currentColor"
    >
      <path d="M480 640h64v-256h-64z" p-id="3274"></path>
      <path d="M512 800a48 48 0 1 0 0-96 48 48 0 0 0 0 96" p-id="3275"></path>
      <path
        d="M512 195.2L874.272 864H149.728L512 195.2z m443.744 685.76l0.384-0.192-416-768-0.384 0.224A31.584 31.584 0 0 0 512 96a31.584 31.584 0 0 0-27.744 16.96l-0.384-0.192-416 768 0.384 0.224A31.2 31.2 0 0 0 64 896a32 32 0 0 0 32 32h832a32 32 0 0 0 32-32 31.2 31.2 0 0 0-4.256-15.04z"
        p-id="3276"
      ></path>
    </svg>
  ),
  info: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="3273"
      className="w-6 h-6"
      fill="currentColor"
    >
      <path
        d="M512 896C299.936 896 128 724.064 128 512S299.936 128 512 128s384 171.936 384 384-171.936 384-384 384m0-832C264.96 64 64 264.96 64 512s200.96 448 448 448 448-200.96 448-448S759.04 64 512 64"
        p-id="3274"
      ></path>
      <path
        d="M480 768h64v-288h-64zM512 272a48 48 0 1 0 0 96 48 48 0 0 0 0-96"
        p-id="3275"
      ></path>
    </svg>
  ),
};

//
const Duration = 0.3;
const Index = (props: IProps) => {
  const { content, type } = props;
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    let times = content.length > 50 ? 3 : 2;
    if (visible) {
      setTimeout(() => {
        setVisible(false);
      }, times * 1000);
    }
  }, [visible]);

  return (
    <AnimatePresence mode="wait">
      {visible && (
        <motion.div
          className={`
              fixed gap-2 top-0 left-1/2 -translate-x-1/2 min-w-56
              ${Colors[type]} border break-all px-6 py-2
              rounded-md flex flex-wrap justify-center items-center text-xl
              shadow-lg shadow-gray-300 z-[99]
              `}
          initial={{
            y: "0",
            opacity: 0,
          }}
          exit={{
            y: "0",
            opacity: 0,
            transition: {
              duration: Duration,
            },
          }}
          animate={{
            y: "20px",
            opacity: 1,
            transition: {
              duration: Duration,
            },
          }}
        >
          {Icon[type]}
          <span className="flex-1">{content}</span>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Index;
