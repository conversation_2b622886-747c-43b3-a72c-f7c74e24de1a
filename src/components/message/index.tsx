"use client";
import { createRoot, Root } from "react-dom/client";
import Message from "./component";

let MessageRoot: Root | null = null;
const message = (
  content: string,
  type: "success" | "error" | "info" | "warning" = "info"
) => {
  let el = document.querySelector("#message-wrapper");
  if (!el) {
    el = document.createElement("div");
    el.id = "message-wrapper";
    document.body.appendChild(el);
  }
  if (MessageRoot) {
    MessageRoot?.unmount();
    MessageRoot = null;
  }
  MessageRoot = MessageRoot ? MessageRoot : createRoot(el);
  MessageRoot.render(<Message type={type} content={content} />);
};

export const success = (content?: string) => {
  message(content || "success", "success");
};

export const error = (content?: string) => {
  message(content || "error", "error");
};

export const info = (content?: string) => {
  message(content || "info", "info");
};

export const warning = (content?: string) => {
  message(content || "warning", "warning");
};

export default { success, error, info, warning };
