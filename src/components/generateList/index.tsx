"use client";

import { useEffect, useState } from "react";
import { getPlanList } from "@/services/plan";
import Link from "next/link";
import message from "../message";
const Index = () => {
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  useEffect(() => {
    handleGetData();
  }, []);

  const handleGetData = async () => {
    const user_id = localStorage.getItem("user_id");

    setLoading(true);
    const result: any = await getPlanList(user_id).finally(() =>
      setLoading(false)
    );

    if (result.code === 0) {
      setData(result.data.plans || []);
    }
  };

  return (
    <>
      <div className="text-2xl text-gray-700 flex justify-between items-center">
        <span>Generate Videos</span>
        <div className="flex gap-2">
          <div className="rounded-full border p-1 border-gray-300 group hover:bg-blue-200 hover:border-[#3d86ef] cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              className="group-hover:text-[#3d86ef]"
            >
              <path d="m15 18-6-6 6-6"></path>
            </svg>
          </div>
          <div className="rounded-full border p-1 border-gray-300 group hover:bg-blue-200 hover:border-[#3d86ef] cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              className="rotate-180 group-hover:text-[#3d86ef] cursor-pointer"
            >
              <path d="m15 18-6-6 6-6"></path>
            </svg>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="h-64">loading...</div>
      ) : (
        <>
          {/* empty */}
          {data?.length === 0 ? (
            <div className="flex mt-4 h-64 items-center justify-center bg-gray-200 rounded-2xl text-lg">
              <span>Generate Video will appear here,</span>
              <Link href="/generate" className="text-blue-500 ml-2 font-bold">
                Starting creating!
              </Link>
            </div>
          ) : (
            <div className="grid lg:grid-cols-4 gap-6 mt-4 grid-cols-1">
              {data.map((item: any, index: number) => {
                return (
                  <Link href={`/product?plan_id=${item.plan_id}`} key={index}>
                    <div className="xl:h-64 lg:h-44 h-80 flex-1 bg-gray-200 rounded-2xl relative overflow-hidden">
                      <img
                        src={item.cover_image}
                        alt=""
                        className="w-full h-full object-contain rounded-2xl"
                      />
                      <div className="absolute bottom-0 left-0 w-full bg-black/20 bg-opacity-50 px-5 py-1 text-white">
                        {item.name}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default Index;
