"use client";
import { useState, useRef, RefObject } from "react";

type IProps = Readonly<{
  inputRef: RefObject<HTMLInputElement | null>;
  onImageUpload?: (base64: string, file_name: string) => void;
}>;

const Index = (props: IProps) => {
  const { onImageUpload, inputRef } = props;

  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        const file_name = file.name;
        onImageUpload && onImageUpload(result, file_name);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        const file_name = file.name;
        onImageUpload && onImageUpload(result, file_name);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClick = () => {
    inputRef?.current?.click();
  };

  return (
    <div
      className={`flex flex-col items-center justify-center border border-dashed rounded-lg cursor-pointer transition-colors w-48 h-48 py-6 text-gray-400 ${
        isDragging
          ? "border-blue-400 bg-blue-900/30"
          : "border-gray-300 hover:border-[#3d86ef] hover:text-[#3d86ef]"
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
    >
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="30702"
        width="32"
        height="32"
      >
        <path
          d="M0 192c0-70.6 57.4-128 128-128h768c70.6 0 128 57.4 128 128v640c0 70.6-57.4 128-128 128H128c-70.6 0-128-57.4-128-128V192z m647.6 213c-9-13.2-23.8-21-39.6-21s-30.8 7.8-39.6 21l-174 255.2-53-66.2c-9.2-11.4-23-18-37.4-18s-28.4 6.6-37.4 18l-128 160c-11.6 14.4-13.8 34.2-5.8 50.8S157.6 832 176 832h672c17.8 0 34.2-9.8 42.4-25.6s7.2-34.8-2.8-49.4l-240-352zM224 384a96 96 0 1 0 0-192 96 96 0 1 0 0 192z"
          p-id="30703"
          fill="currentColor"
        ></path>
      </svg>
      <span className="mt-2">Add Image</span>
      <input
        type="file"
        ref={inputRef}
        className="hidden"
        accept="image/*"
        onChange={handleFileChange}
      />
    </div>
  );
};

export default Index;
