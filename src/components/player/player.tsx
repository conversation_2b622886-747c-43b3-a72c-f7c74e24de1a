import { motion } from "motion/react";

type IProps = {
  src: string;
  show: boolean;
  onClose: () => void;
};
const Index = (props: IProps) => {
  const { src, onClose, show } = props;
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black z-50" onClick={onClose}>
      <div className="absolute top-5 right-5 z-10 bg-gray-200 p-2 rounded-full cursor-pointer">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-8 h-8 cursor-pointer"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </div>
      <video
        src={src}
        controls
        className="w-full h-full"
        autoPlay
        muted
      ></video>
    </div>
  );
};

export default Index;
