import { useEffect, useRef, useState } from "react";
import Video from "./video";
import Image from "./image";
import Player from "./player";

type IProps = {
  url: string;
};

const getBlob = (url: string) => {
  return new Promise<string>((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url, true);
    xhr.responseType = "blob";
    xhr.onload = function () {
      if (xhr.status === 200) {
        let url = URL.createObjectURL(xhr.response);
        resolve(url);
      } else {
        reject("Error: " + xhr.status);
      }
    };
    xhr.onerror = function () {
      reject("Error: " + xhr.status);
    };
    xhr.send();
  });
};

const drawVideo = (video: HTMLVideoElement) => {
  return new Promise((resolve, reject) => {
    const cvs = document.createElement("canvas");
    const ctx = cvs.getContext("2d");
    cvs.width = video.videoWidth;
    cvs.height = video.videoHeight;
    ctx?.drawImage(video, 0, 0, cvs.width, cvs.height);
    cvs.toBlob((blob) => {
      let url = blob ? URL.createObjectURL(blob) : "";
      resolve(url);
    });
  });
};

// 获取首帧url
const captureFrame = async (url: string, time = 0) => {
  let blob: string = await getBlob(url);
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.currentTime = time;
    video.muted = true;
    video.autoplay = true;
    video.crossOrigin = "anonymous";
    video.oncanplay = async () => {
      let img = await drawVideo(video);
      resolve({ img, video });
    };

    video.src = blob;
  });
};

const Index = (props: IProps) => {
  const { url } = props;
  const [firstFrame, setFirstFrame] = useState<string>("");
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [showPlayer, setShowPlayer] = useState<boolean>(false);
  useEffect(() => {
    handleFirstFrame();
  }, []);

  const handleFirstFrame = async () => {
    const result: any = await captureFrame(url);
    setFirstFrame(result.img);
  };

  return (
    <>
      <div
        onMouseEnter={() => setIsPlaying(true)}
        onMouseLeave={() => setIsPlaying(false)}
        onClick={() => {
          setShowPlayer(true);
        }}
        className="w-full relative border rounded-xl border-transparent shadow-lg shadow-[#00c8ff]/10 overflow-hidden"
      >
        {/* 判断是否播放 */}
        {isPlaying ? (
          <Video url={url} image={firstFrame} />
        ) : (
          <Image url={firstFrame}></Image>
        )}

        {/* 背景 */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px)`,
            backgroundSize: "20px 20px",
          }}
        ></div>
        <div
          className="absolute inset-0"
          style={{
            background:
              "linear-gradient(to bottom, transparent 70%, rgba(0, 0, 0, 0.7) 100%)",
          }}
        ></div>
      </div>

      <Player
        src={url}
        show={showPlayer}
        onClose={() => {
          setShowPlayer(false);
        }}
      ></Player>
    </>
  );
};

export default Index;
