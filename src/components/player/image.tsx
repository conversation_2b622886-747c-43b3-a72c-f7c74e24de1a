import { useEffect } from "react";

type IProps = {
  url: string;
};

const SkeletonContent = () => (
  <div className="p-4 w-[80%] h-[80%] bg-blue-500 rounded-lg"></div>
);

const Index = (props: IProps) => {
  const { url } = props;

  return (
    <>
      {url ? (
        <img
          src={url}
          alt="video poster"
          className="w-full h-full object-contain z-10 relative pointer-events-none"
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-[#3b82f6] border-t-transparent"></div>
        </div>
      )}
    </>
  );
};
export default Index;
