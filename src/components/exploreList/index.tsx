"use client";

import { useEffect, useState } from "react";
import { getHomeData } from "@/services/plan";
import Video from "@/components/player";
import FilterBar from "./filterBar";
const Index = () => {
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  useEffect(() => {
    handleGetData();
  }, []);

  const handleGetData = async () => {
    setLoading(true);
    const result: any = await getHomeData().finally(() => setLoading(false));

    if (result.code === 0) {
      setData(result.data.video_infos);
    }
  };
  return (
    <>
      <FilterBar />
      {/* <div className="mt-4 text-2xl text-gray-700 flex justify-between items-center">
        <span>Model Clothes</span>
        <div className="flex gap-2">
          <div className="rounded-full border p-1 border-gray-300 group hover:bg-blue-200 hover:border-[#3d86ef] cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              className="group-hover:text-[#3d86ef]"
            >
              <path d="m15 18-6-6 6-6"></path>
            </svg>
          </div>
          <div className="rounded-full border p-1 border-gray-300 group hover:bg-blue-200 hover:border-[#3d86ef] cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              className="rotate-180 group-hover:text-[#3d86ef] cursor-pointer"
            >
              <path d="m15 18-6-6 6-6"></path>
            </svg>
          </div>
        </div>
      </div> */}
      <div className="min-h-40 relative">
        {/* //TODO:loading */}
        {loading && (
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-lg bg-black/70 text-white px-2 py-1 rounded-md z-[999]">
            Loading...
          </div>
        )}
        <div className="grid grid-cols-1 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 gap-4 mt-8">
          {data?.map((item: any, index: number) => {
            return (
              <div
                key={index}
                className="xl:h-80 h-60 max-h-80 bg-gray-300 rounded-2xl flex overflow-hidden justify-center"
              >
                <Video url={item.video_url} />
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default Index;
