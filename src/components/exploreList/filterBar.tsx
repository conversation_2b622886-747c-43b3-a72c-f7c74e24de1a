"use client";

import Link from "next/link";
import { useState } from "react";

const Item = ["All", "Clothing", "Automotive"];
const Index = () => {
  const [currIndex, setCurrIndex] = useState(0);

  return (
    <div className="flex justify-between items-center">
      <div className="flex flex-row gap-4 items-center">
        {Item.map((text, index) => {
          return (
            <div
              key={index}
              onClick={() => setCurrIndex(index)}
              className={` text-lg px-3.5 py-1 cursor-pointer border-b-2  transition-all duration-200 ease-in-out
                ${
                  currIndex === index
                    ? "border-blue-500"
                    : "border-transparent hover:border-blue-300"
                }`}
            >
              {text}
            </div>
          );
        })}
      </div>
      <Link href={"/generate"}>
        <button className=" bg-blue-600 text-white px-3.5 py-2 rounded-[4px] cursor-pointer">
          Generate
        </button>
      </Link>
    </div>
  );
};

export default Index;
