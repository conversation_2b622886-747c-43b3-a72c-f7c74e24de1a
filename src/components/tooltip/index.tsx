import { motion } from "motion/react";

type IProps = {
  title: string;
  visible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
};
// 弹窗组件
const Index = (props: IProps) => {
  const { title, visible, onClose, children } = props;
  if (!visible) return null;
  return (
    <div className="fixed inset-0 bg-black/40 backdrop-blur-xs bg-opacity-50 flex items-center justify-center z-40">
      <motion.div
        className="fixed flex flex-col bg-white rounded-lg shadow-lg py-8 px-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 overflow-hidden"
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="text-2xl font-bold mb-4">{title}</div>
        <div
          className="absolute top-0 right-0 h-10 w-10 text-3xl text-gray-500 hover:text-gray-800 bg-gray-200 cursor-pointer flex justify-center items-center"
          onClick={onClose}
        >
          ×
        </div>
        <div className="py-4 flex-1">{children}</div>
      </motion.div>
    </div>
  );
};

export default Index;
