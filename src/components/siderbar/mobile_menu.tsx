"use client";
import { motion } from "motion/react";
import UserCtrl from "@/components/user_ctrl";
import { useUserStore } from "@/providers/user-store-provider";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

type IProps = {
  menu: Array<any>;
  logo: string;
};
const Index = (props: IProps) => {
  const { menu, logo: Logo } = props;
  const [showMenu, setShowMenu] = useState(false);
  const pathname = usePathname();
  const [logo, setLogo] = useState(Logo);
  const { isLogin, logout, fetchInitUser } = useUserStore((state) => state);

  const handleLogout = () => {
    logout();
  };
  return (
    <div
      className={`
        block lg:hidden fixed z-50 ${
          showMenu &&
          "bg-black/50 backdrop-blur-xs h-screen w-screen top-0 left-0"
        }  `}
      onClick={() => setShowMenu(false)}
    >
      {/* toggle button */}
      <div
        className="absolute top-7 left-5 p-1 z-10"
        onClick={(e) => {
          e.stopPropagation();
          setShowMenu(!showMenu);
        }}
      >
        {showMenu ? (
          <svg
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            className="w-8 h-8"
          >
            <path
              d="M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84l-183.04-183.466667a42.666667 42.666667 0 0 0-60.586667 60.586667l183.466667 183.04-183.466667 183.04a42.666667 42.666667 0 0 0 0 60.586667 42.666667 42.666667 0 0 0 60.586667 0l183.04-183.466667 183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 0 42.666667 42.666667 0 0 0 0-60.586667z"
              p-id="2987"
              fill="#1A2029"
            ></path>
          </svg>
        ) : (
          <svg
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            className="w-8 h-8"
          >
            <path
              d="M195.584 245.76h571.392A31.66208 31.66208 0 0 1 798.72 277.36064v15.79008a31.66208 31.66208 0 0 1-31.744 31.60064H195.584A31.66208 31.66208 0 0 1 163.84 293.15072v-15.79008A31.66208 31.66208 0 0 1 195.584 245.76z m0 236.97408H608.256a31.66208 31.66208 0 0 1 31.744 31.60064v15.79008a31.66208 31.66208 0 0 1-31.744 31.60064H195.584A31.66208 31.66208 0 0 1 163.84 530.14528v-15.81056a31.66208 31.66208 0 0 1 31.744-31.60064z m0 236.97408h571.392A31.68256 31.68256 0 0 1 798.72 751.32928v15.81056A31.66208 31.66208 0 0 1 766.976 798.72H195.584A31.66208 31.66208 0 0 1 163.84 767.11936v-15.79008a31.66208 31.66208 0 0 1 31.744-31.60064z"
              fill="#1A2029"
              p-id="1475"
            ></path>
          </svg>
        )}
      </div>

      {/* menu */}
      {showMenu && (
        <motion.div
          className={`absolute ${
            true ? "block" : "hidden"
          } top-0 w-screen h-screen bg-white py-20`}
          initial={{ x: "-100%" }}
          animate={{ x: 0 }}
          transition={{ duration: 0.2 }}
        >
          <ul className="flex flex-col gap-4 p-4 text-gray-400 select-none">
            {menu.map((item, index) => (
              <Link key={index} href={item.path} shallow={false}>
                <li
                  className={`text-lg px-5 py-3 rounded-2xl flex items-center h-13 whitespace-nowrap
                ${
                  item.path === pathname
                    ? "bg-blue-600 shadow-md shadow-blue-400 text-white"
                    : "hover:bg-blue-100"
                }`}
                >
                  <span className="min-w-max">{item.icon}</span>
                  <span
                    className={`transform-none ${
                      true ? "block ml-2 duration-500" : "hidden"
                    } overflow-hidden`}
                  >
                    {item.name}
                  </span>
                </li>
              </Link>
            ))}
          </ul>

          <div className="absolute bottom-0 left-0 w-full p-4">
            <div className="flex items-center justify-center p-2 gap-6">
              <UserCtrl />
            </div>

            <Link href={"/login"} onClick={handleLogout}>
              <div className="flex items-center justify-center rounded-xl text-lg p-2 hover:bg-blue-500 hover:text-white select-none">
                <svg
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="29597"
                  width="16"
                  height="16"
                  fill="currentColor"
                >
                  <path
                    d="M918.857143 763.428571h-80.342857c-5.485714 0-10.628571 2.4-14.057143 6.628572-8 9.714286-16.571429 19.085714-25.6 28a404.388571 404.388571 0 0 1-128.8 86.742857A403.2 403.2 0 0 1 512.457143 916.571429c-54.742857 0-107.771429-10.742857-157.6-31.771429a404.388571 404.388571 0 0 1-128.8-86.742857 403.748571 403.748571 0 0 1-86.857143-128.571429C118.057143 619.657143 107.428571 566.742857 107.428571 512s10.742857-107.657143 31.771429-157.485714c20.342857-48.114286 49.6-91.428571 86.857143-128.571429s80.571429-66.4 128.8-86.742857c49.828571-21.028571 102.857143-31.771429 157.6-31.771429 54.742857 0 107.771429 10.628571 157.6 31.771429 48.228571 20.342857 91.542857 49.6 128.8 86.742857 9.028571 9.028571 17.485714 18.4 25.6 28 3.428571 4.228571 8.685714 6.628571 14.057143 6.628572H918.857143c7.2 0 11.657143-8 7.657143-14.057143C838.857143 110.285714 685.485714 20.114286 511.2 20.571429 237.371429 21.257143 17.828571 243.542857 20.571429 517.028571 23.314286 786.171429 242.514286 1003.428571 512.457143 1003.428571c173.828571 0 326.514286-90.057143 414.057143-225.942857 3.885714-6.057143-0.457143-14.057143-7.657143-14.057143z m101.6-258.628571L858.285714 376.8c-6.057143-4.8-14.857143-0.457143-14.857143 7.2v86.857143H484.571429c-5.028571 0-9.142857 4.114286-9.142858 9.142857v64c0 5.028571 4.114286 9.142857 9.142858 9.142857h358.857142v86.857143c0 7.657143 8.914286 12 14.857143 7.2l162.171429-128a9.142857 9.142857 0 0 0 0-14.4z"
                    p-id="29598"
                  ></path>
                </svg>

                <span className="ml-4">{isLogin ? "Logout" : "Login"}</span>
              </div>
            </Link>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Index;
