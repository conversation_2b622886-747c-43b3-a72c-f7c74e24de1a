"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useUserStore } from "@/providers/user-store-provider";
import MobileMenu from "./mobile_menu";
import Modal from "@/components/modal";
const Menu = [
  {
    path: "/",
    name: "Home",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
        />
      </svg>
    ),
  },
  {
    path: "/generate/",
    name: "Generate",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z"
        />
      </svg>
    ),
  },
  {
    path: "/plans/",
    name: "Library",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
        />
      </svg>
    ),
  },
];
const Logo = "Dream Loom";

const Index = () => {
  const pathname = usePathname();
  const [logo, setLogo] = useState(Logo);
  const [toggle, setToggle] = useState(true);
  const { isLogin, logout, fetchInitUser } = useUserStore((state) => state);

  const [wechatShow, setWechatShow] = useState(false);

  useEffect(() => {
    // 如果未登录，则获取用户信息
    !isLogin && fetchInitUser();
  }, []);

  const handleLogout = () => {
    logout();
  };

  // left side bar
  return (
    <>
      <div className="hidden lg:flex flex-col flex-1 relative">
        <div
          className={`relative flex-1 ${toggle ? "w-76" : "w-24"} duration-200`}
        >
          <div
            className="border-gray-800 border-1 rounded-full cursor-pointer absolute -right-[11px] top-9 bg-white z-10"
            onClick={() => setToggle(!toggle)}
          >
            <svg
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              className={`${toggle ? "" : "rotate-180"}`}
              p-id="8674"
              width="21"
              height="21"
            >
              <path
                d="M576 672c-6.4 0-19.2 0-25.6-6.4l-128-128c-12.8-12.8-12.8-32 0-44.8l128-128c12.8-12.8 32-12.8 44.8 0s12.8 32 0 44.8L492.8 512l102.4 102.4c12.8 12.8 12.8 32 0 44.8C595.2 672 582.4 672 576 672z"
                fill="#272636"
                p-id="8675"
              ></path>
            </svg>
          </div>

          {/* logo */}
          <div className="h-24 flex items-center justify-center select-none whitespace-nowrap">
            <Link href="/" className="text-3xl font-bold">
              {toggle ? logo : logo.slice(0, 1)}
            </Link>
          </div>

          {/* menu */}
          <nav className="flex-1">
            <ul className="flex flex-col gap-4 p-4 text-gray-400 select-none">
              {Menu.map((item, index) => (
                <Link key={index} href={item.path}>
                  <li
                    className={`text-lg px-5 py-3 rounded-2xl flex items-center h-13 whitespace-nowrap
                ${
                  item.path === pathname
                    ? "bg-blue-600 shadow-md shadow-blue-400 text-white"
                    : "hover:bg-blue-100"
                }`}
                  >
                    <span className="min-w-max">{item.icon}</span>
                    <span
                      className={`transform-none ${
                        toggle ? "block ml-2 duration-500" : "hidden"
                      } overflow-hidden`}
                    >
                      {item.name}
                    </span>
                  </li>
                </Link>
              ))}
            </ul>

            <div className="border-b-1 border-gray-300 m-5"></div>
            <ul className="flex flex-col gap-4 p-4 text-gray-400 select-none">
              {/* discord */}
              <Link href="https://discord.gg/F6GA8yMjNP" target="_blank">
                <li className="px-5 py-3 rounded-2xl flex items-center h-13 whitespace-nowrap hover:bg-blue-100">
                  <span className="min-w-max">
                    <svg
                      viewBox="0 0 1280 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="1612"
                      id="mx_n_1750528564438"
                      className="w-6 h-6"
                    >
                      <path
                        d="M1049.062 139.672a3 3 0 0 0-1.528-1.4A970.13 970.13 0 0 0 808.162 64.06a3.632 3.632 0 0 0-3.846 1.82 674.922 674.922 0 0 0-29.8 61.2 895.696 895.696 0 0 0-268.852 0 619.082 619.082 0 0 0-30.27-61.2 3.78 3.78 0 0 0-3.848-1.82 967.378 967.378 0 0 0-239.376 74.214 3.424 3.424 0 0 0-1.576 1.352C78.136 367.302 36.372 589.38 56.86 808.708a4.032 4.032 0 0 0 1.53 2.75 975.332 975.332 0 0 0 293.65 148.378 3.8 3.8 0 0 0 4.126-1.352A696.4 696.4 0 0 0 416.24 860.8a3.72 3.72 0 0 0-2.038-5.176 642.346 642.346 0 0 1-91.736-43.706 3.77 3.77 0 0 1-0.37-6.252 502.094 502.094 0 0 0 18.218-14.274 3.638 3.638 0 0 1 3.8-0.512c192.458 87.834 400.82 87.834 591 0a3.624 3.624 0 0 1 3.848 0.466 469.066 469.066 0 0 0 18.264 14.32 3.768 3.768 0 0 1-0.324 6.252 602.814 602.814 0 0 1-91.78 43.66 3.75 3.75 0 0 0-2 5.222 782.11 782.11 0 0 0 60.028 97.63 3.728 3.728 0 0 0 4.126 1.4A972.096 972.096 0 0 0 1221.4 811.458a3.764 3.764 0 0 0 1.53-2.704c24.528-253.566-41.064-473.824-173.868-669.082zM444.982 675.16c-57.944 0-105.688-53.174-105.688-118.478s46.818-118.482 105.688-118.482c59.33 0 106.612 53.64 105.686 118.478 0 65.308-46.82 118.482-105.686 118.482z m390.76 0c-57.942 0-105.686-53.174-105.686-118.478s46.818-118.482 105.686-118.482c59.334 0 106.614 53.64 105.688 118.478 0 65.308-46.354 118.482-105.688 118.482z"
                        p-id="1613"
                        fill="#5765f2"
                      ></path>
                    </svg>
                  </span>
                  <span
                    className={`transform-none ${
                      toggle ? "block ml-2 duration-500" : "hidden"
                    } overflow-hidden`}
                  >
                    discord
                  </span>
                </li>
              </Link>

              {/* wechat */}
              <li
                className="px-5 py-3 rounded-2xl flex items-center h-13 whitespace-nowrap hover:bg-blue-100"
                onClick={() => setWechatShow(true)}
              >
                <span className="min-w-max">
                  <svg
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                  >
                    <path
                      d="M690.090667 377.400889c5.916444 0 11.804444 0.199111 17.635555 0.512-24.433778-128.711111-158.321778-227.100444-319.914666-227.100445-178.801778 0-323.811556 120.604444-323.811556 269.368889 0 81.123556 43.605333 154.225778 111.900444 203.633778a21.504 21.504 0 0 1 7.992889 24.490667c-5.489778 20.309333-14.193778 52.792889-14.592 54.300444-0.711111 2.588444-1.706667 5.205333-1.706666 7.907556 0 5.888 4.807111 10.808889 10.808889 10.808889 2.275556 0 4.209778-0.910222 6.200888-1.991111l70.883556-40.931556c5.319111-3.100444 11.008-5.006222 17.208889-5.006222 3.214222 0 6.4 0.512 9.500444 1.422222a383.943111 383.943111 0 0 0 123.505778 14.392889 205.283556 205.283556 0 0 1-10.894222-65.991111c0-135.822222 132.181333-245.816889 295.281778-245.816889z m-194.275556-86.471111c23.779556 0 43.178667 19.285333 43.178667 43.064889a43.178667 43.178667 0 1 1-86.414222 0c0-23.779556 19.427556-43.093333 43.235555-43.093334z m-215.893333 86.186666a43.121778 43.121778 0 0 1 0-86.186666 43.121778 43.121778 0 1 1 0 86.186666z m586.780444 415.573334c56.888889-41.187556 93.184-101.973333 93.184-169.671111 0-124.017778-120.775111-224.512-269.880889-224.512-148.992 0-269.909333 100.494222-269.909333 224.512 0 123.989333 120.803556 224.483556 269.909333 224.483555 30.805333 0 60.586667-4.408889 88.092445-12.288a26.567111 26.567111 0 0 1 22.186666 2.901334l59.107556 33.991111c1.706667 0.995556 3.299556 1.706667 5.205333 1.706666a8.96 8.96 0 0 0 8.988445-9.016889c0-2.190222-0.881778-4.380444-1.393778-6.599111-0.284444-1.194667-7.594667-28.302222-12.202667-45.283555a22.556444 22.556444 0 0 1-0.881777-5.688889 18.460444 18.460444 0 0 1 7.594666-14.506667z m-266.524444-205.482667c-19.882667 0-35.982222-16.099556-35.982222-35.896889 0-19.797333 16.099556-35.896889 36.010666-35.896889 19.911111 0 35.982222 16.099556 35.982222 35.896889 0 19.797333-16.213333 35.896889-35.982222 35.896889z m179.911111 0c-19.911111 0-35.982222-16.099556-35.982222-35.896889 0-19.797333 16.099556-35.896889 35.982222-35.896889 19.911111 0 36.010667 16.099556 36.010667 35.896889a36.096 36.096 0 0 1-36.010667 35.896889z"
                      fill="#69bb63"
                    ></path>
                  </svg>
                </span>
                <span
                  className={`transform-none ${
                    toggle ? "block ml-2 duration-500" : "hidden"
                  } overflow-hidden`}
                >
                  wechat
                </span>
              </li>

              {/* support */}
              <Link href={"mailto:<EMAIL>"}>
                <li className="px-5 py-3 rounded-2xl flex items-center h-13 whitespace-nowrap hover:bg-blue-100">
                  <span className="min-w-max">
                    <svg
                      className="w-6 h-6 "
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M926.47619 355.644952V780.190476a73.142857 73.142857 0 0 1-73.142857 73.142857H170.666667a73.142857 73.142857 0 0 1-73.142857-73.142857V355.644952l304.103619 257.828572a170.666667 170.666667 0 0 0 220.745142 0L926.47619 355.644952zM853.333333 170.666667a74.044952 74.044952 0 0 1 26.087619 4.778666 72.704 72.704 0 0 1 30.622477 22.186667 73.508571 73.508571 0 0 1 10.678857 17.67619c3.169524 7.509333 5.12 15.652571 5.607619 24.210286L926.47619 243.809524v24.380952L559.469714 581.241905a73.142857 73.142857 0 0 1-91.306666 2.901333l-3.632762-2.925714L97.52381 268.190476v-24.380952a72.899048 72.899048 0 0 1 40.155428-65.292191A72.97219 72.97219 0 0 1 170.666667 170.666667h682.666666z"
                        fill="currentColor"
                      ></path>
                    </svg>
                  </span>
                  <span
                    className={`transform-none ${
                      toggle ? "block ml-2 duration-500" : "hidden"
                    } overflow-hidden`}
                  >
                    support
                  </span>
                </li>
              </Link>
            </ul>
          </nav>
        </div>

        {/* login or logout */}
        <div className="border-0 border-gray-300 p-5 border-t-1">
          <Link href={"/login"} onClick={handleLogout}>
            <div className="flex items-center justify-center rounded-xl text-lg p-2 hover:bg-blue-500 hover:text-white select-none">
              <svg
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="29597"
                width="16"
                height="16"
                fill="currentColor"
              >
                <path
                  d="M918.857143 763.428571h-80.342857c-5.485714 0-10.628571 2.4-14.057143 6.628572-8 9.714286-16.571429 19.085714-25.6 28a404.388571 404.388571 0 0 1-128.8 86.742857A403.2 403.2 0 0 1 512.457143 916.571429c-54.742857 0-107.771429-10.742857-157.6-31.771429a404.388571 404.388571 0 0 1-128.8-86.742857 403.748571 403.748571 0 0 1-86.857143-128.571429C118.057143 619.657143 107.428571 566.742857 107.428571 512s10.742857-107.657143 31.771429-157.485714c20.342857-48.114286 49.6-91.428571 86.857143-128.571429s80.571429-66.4 128.8-86.742857c49.828571-21.028571 102.857143-31.771429 157.6-31.771429 54.742857 0 107.771429 10.628571 157.6 31.771429 48.228571 20.342857 91.542857 49.6 128.8 86.742857 9.028571 9.028571 17.485714 18.4 25.6 28 3.428571 4.228571 8.685714 6.628571 14.057143 6.628572H918.857143c7.2 0 11.657143-8 7.657143-14.057143C838.857143 110.285714 685.485714 20.114286 511.2 20.571429 237.371429 21.257143 17.828571 243.542857 20.571429 517.028571 23.314286 786.171429 242.514286 1003.428571 512.457143 1003.428571c173.828571 0 326.514286-90.057143 414.057143-225.942857 3.885714-6.057143-0.457143-14.057143-7.657143-14.057143z m101.6-258.628571L858.285714 376.8c-6.057143-4.8-14.857143-0.457143-14.857143 7.2v86.857143H484.571429c-5.028571 0-9.142857 4.114286-9.142858 9.142857v64c0 5.028571 4.114286 9.142857 9.142858 9.142857h358.857142v86.857143c0 7.657143 8.914286 12 14.857143 7.2l162.171429-128a9.142857 9.142857 0 0 0 0-14.4z"
                  p-id="29598"
                ></path>
              </svg>
              {toggle && (
                <span className="ml-4">{isLogin ? "Logout" : "Login"}</span>
              )}
            </div>
          </Link>
        </div>
      </div>

      {/* mobile menu */}
      <MobileMenu menu={Menu} logo={Logo} />

      {/* discord qrcode */}
      <Modal
        title="Wechat QR Code"
        visible={wechatShow}
        onClose={() => setWechatShow(false)}
      >
        <div className="w-80 h-80 bg-blue-200 border border-gray-50 rounded-lg animate-pulse flex justify-center items-center">
          <img src="/wechart.jpeg" alt="" />
        </div>
      </Modal>
    </>
  );
};

export default Index;
