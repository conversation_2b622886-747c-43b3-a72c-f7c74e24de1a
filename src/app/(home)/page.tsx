import Title from "@/components/title";
import Link from "next/link";
import ExploreList from "@/components/exploreList";
import GenerateList from "@/components/generateList";

const QuickAction = [
  {
    title: "Generate Video",
    key: "generate",
    desc: "Generate video based on uploaded images",
    href: "/generate",
    icon: (
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="24341"
        width="32"
        height="32"
      >
        <path
          d="M810.666667 192a21.333333 21.333333 0 0 1 21.333333 21.333333v597.333334a21.333333 21.333333 0 0 1-21.333333 21.333333H213.333333a21.333333 21.333333 0 0 1-21.333333-21.333333V213.333333a21.333333 21.333333 0 0 1 21.333333-21.333333h597.333334zM213.333333 128a85.333333 85.333333 0 0 0-85.333333 85.333333v597.333334a85.333333 85.333333 0 0 0 85.333333 85.333333h597.333334a85.333333 85.333333 0 0 0 85.333333-85.333333V213.333333a85.333333 85.333333 0 0 0-85.333333-85.333333H213.333333z"
          fill="#3d86ef"
          p-id="24342"
        ></path>
        <path
          d="M309.333333 512a32 32 0 0 1 32-32h341.333334a32 32 0 0 1 0 64H341.333333a32 32 0 0 1-32-32z"
          fill="#3d86ef"
          p-id="24343"
        ></path>
        <path
          d="M512 714.666667a32 32 0 0 1-32-32V341.333333a32 32 0 0 1 64 0v341.333334a32 32 0 0 1-32 32z"
          fill="#3d86ef"
          p-id="24344"
        ></path>
      </svg>
    ),
  },

  {
    title: "My Generate Videos",
    key: "videos",
    desc: "Enter the video list to view all the videos I generated",
    href: "/plans",
    icon: (
      <svg
        viewBox="0 0 1109 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="21974"
        width="32"
        height="32"
      >
        <path
          d="M853.333333 516.181333H768V170.666667a85.333333 85.333333 0 0 0-85.333333-85.333334H170.666667a85.333333 85.333333 0 0 0-85.333334 85.333334v682.666666a85.333333 85.333333 0 0 0 85.333334 85.333334h255.402666v85.333333H170.666667a170.666667 170.666667 0 0 1-170.666667-170.666667V170.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h512a170.666667 170.666667 0 0 1 170.666666 170.666667v345.514666z"
          fill="#3d86ef"
          p-id="21975"
        ></path>
        <path
          d="M213.333333 341.333333a42.666667 42.666667 0 0 1 0-85.333333h341.333334a42.666667 42.666667 0 0 1 0 85.333333h-341.333334zM213.333333 512a42.666667 42.666667 0 0 1 0-85.333333h256a42.666667 42.666667 0 0 1 0 85.333333h-256zM993.792 560.896l60.416 60.330667-350.890667 350.805333-206.848-206.848 60.330667-60.330667 146.517333 146.517334z"
          fill="#3d86ef"
          p-id="21976"
        ></path>
      </svg>
    ),
  },
  {
    title: "Account Settings",
    key: "setting",
    desc: "Modify personal information and ai default prompts",
    href: "/setting",
    icon: (
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="23159"
        width="32"
        height="32"
      >
        <path
          d="M812.6976 195.976533L936.021333 409.6a204.8 204.8 0 0 1 0 204.8L812.714667 828.023467a204.8 204.8 0 0 1-177.373867 102.4H388.676267a204.8 204.8 0 0 1-177.373867-102.4L87.978667 614.4a204.8 204.8 0 0 1 0-204.8l123.323733-213.623467a204.8 204.8 0 0 1 177.373867-102.4h246.647466a204.8 204.8 0 0 1 177.373867 102.4z m-59.118933 34.133334a136.533333 136.533333 0 0 0-118.254934-68.266667H388.676267a136.533333 136.533333 0 0 0-118.254934 68.266667L147.114667 443.733333a136.533333 136.533333 0 0 0 0 136.533334l123.323733 213.623466a136.533333 136.533333 0 0 0 118.254933 68.266667h246.647467a136.533333 136.533333 0 0 0 118.254933-68.266667L876.885333 580.266667a136.533333 136.533333 0 0 0 0-136.533334l-123.323733-213.623466z"
          fill="#3d86ef"
          p-id="23160"
        ></path>
        <path
          d="M512 682.666667c94.2592 0 170.666667-76.407467 170.666667-170.666667s-76.407467-170.666667-170.666667-170.666667-170.666667 76.407467-170.666667 170.666667 76.407467 170.666667 170.666667 170.666667z m0-68.266667a102.4 102.4 0 1 1 0-204.8 102.4 102.4 0 0 1 0 204.8z"
          fill="#3d86ef"
          p-id="23161"
        ></path>
      </svg>
    ),
  },
];

const Index = async () => {
  return (
    <>
      {/* title */}
      <Title desc="Discover the latest, most amazing AI-generated videos, or create your own creations.">
        Home
      </Title>

      {/* Explore */}
      <div className="mt-12">
        <ExploreList />
      </div>
    </>
  );
};

export default Index;
