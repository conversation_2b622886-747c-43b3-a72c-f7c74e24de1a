"use client";

import SiderBar from "@/components/siderbar";
import Header from "@/components/header";
const Index = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <>
      {/* left */}
      <div className="left border-0 border-gray-200 border-r-1 flex flex-col">
        <SiderBar />
      </div>
      {/* right */}
      <div className="flex-1 right overflow-auto">
        <Header />
        <main className="p-8">{children}</main>
      </div>
    </>
  );
};

export default Index;
