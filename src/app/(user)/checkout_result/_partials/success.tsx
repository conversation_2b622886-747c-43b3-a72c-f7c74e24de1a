"use client";

import { useEffect, useState } from "react";

const Index = () => {
  useEffect(() => {
    setTimeout(() => {
      window.location.href = "/";
    }, 3000);
  }, []);
  const [time, setTime] = useState(3);

  useEffect(() => {
    const interval = setInterval(() => {
      setTime((prevTime) => prevTime - 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [time]);

  return (
    <div>
      <div className="flex flex-col items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-24 h-24 text-green-500"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M4.5 12.75l6 6 9-13.5"
          />
        </svg>
        <h1 className="text-2xl font-bold text-gray-900">
          Checkout Successful
        </h1>
        <p className="text-gray-500">
          Your order has been placed successfully.
        </p>
      </div>
      <div className="mt-4">
        <p className="text-gray-500">
          Redirecting to home page in {time} seconds...
        </p>
      </div>
    </div>
  );
};

export default Index;
