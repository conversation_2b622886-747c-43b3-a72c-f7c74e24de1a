"use client";

import message from "@/components/message";
import { checkout } from "@/services/user";
import { useState } from "react";

type IProps = {
  title: string;
  desc: string;
  price: string;
  features: string[];
};

const Index = (props: IProps) => {
  const { title, desc, price, features } = props;
  const [loading, setLoading] = useState(false);
  const handleCheckout = async () => {
    setLoading(true);
    const res: any = await checkout().finally(() => setLoading(false));
    if (res.status === "success") {
      message.success("Checkout success, redirecting to payment page...");
      window.location.href = res.checkout_url;
    }
  };
  return (
    <div className="min-h-full w-full bg-gray-100 p-6 rounded-lg outline outline-gray-300 flex-1 flex flex-col justify-between hover:outline-2 hover:outline-blue-500">
      <div className="mb-4 text-2xl font-bold text-gray-700">{title}</div>
      <div>
        <p className="text-gray-600">{desc}</p>
      </div>
      <div className="mt-10">
        <span className=" text-3xl">{price}</span>
        <span className=" text-gray-500"> / month</span>
      </div>
      <div className="mt-4 flex-1">
        {features?.map((feature, index) => {
          return (
            <div className="flex items-center gap-2 py-1" key={index}>
              <svg
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5 text-green-500"
              >
                <path
                  d="M432 709.248l-166.624-166.624 45.248-45.248 121.376 121.376 281.376-281.376 45.248 45.248L432 709.248zM512 64C264.576 64 64 264.576 64 512s200.576 448 448 448 448-200.576 448-448S759.424 64 512 64z"
                  fill="currentColor"
                  p-id="3267"
                ></path>
              </svg>
              <span className="text-gray-600">{feature}</span>
            </div>
          );
        })}
      </div>
      <div className="mt-4">
        <button
          className={`mt-10 w-full bg-blue-500 text-white text-lg py-2 rounded-md cursor-pointer hover:bg-blue-600
            ${loading ? "pointer-events-none opacity-50" : ""}
            `}
          onClick={handleCheckout}
        >
          {loading ? "Loading..." : "Get Started"}
        </button>
      </div>
    </div>
  );
};

export default Index;
