"use client";
import Card from "./card";

const plans = [
  {
    title: "Basic",
    desc: "For small teams and projects",
    price: "$9.99",
    features: [
      "1 User",
      "5 Projects",
      "10 Tasks",
      "Basic Analytics",
      "Limited Support",
    ],
  },
  {
    title: "Standard",
    desc: "For medium teams and projects",
    price: "$19.99",
    features: [
      "5 Users",
      "20 Projects",
      "50 Tasks",
      "Advanced Analytics",
      "Priority Support",
    ],
  },
  {
    title: "Pro",
    desc: "For large teams and projects",
    price: "$29.99",
    features: [
      "Unlimited Users",
      "Unlimited Projects",
      "Unlimited Tasks",
      "Advanced Analytics",
      "Priority Support",
    ],
  },
  {
    title: "Premier",
    desc: "For enterprise teams and projects",
    price: "$49.99",
    features: [
      "Unlimited Users",
      "Unlimited Projects",
      "Unlimited Tasks",
      "Advanced Analytics",
      "Priority Support",
      "Customizable branding",
    ],
  },
];

const SubscribePlan = () => {
  return (
    <div className="flex gap-4 md:flex-row md:gap-8 py-8 px-4 h-full">
      {plans.map((item, i) => {
        return (
          <Card
            key={i}
            title={item.title}
            desc={item.desc}
            price={item.price}
            features={item.features}
          />
        );
      })}
    </div>
  );
};

export default SubscribePlan;
