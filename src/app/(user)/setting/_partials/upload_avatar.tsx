"use client";

import { uploadImage } from "@/services/plan";
import { useEffect, useRef, useState } from "react";

type IProps = {
  avatar?: string;
  onChange: (img: string) => void;
};

const Index = (props: IProps) => {
  const { avatar, onChange } = props;
  const [loading, setLoading] = useState(false);
  const [loadingText, setLoadingText] = useState<string>("");
  const [img, setImg] = useState<string>();
  const inputRef = useRef<HTMLInputElement>(null);
  const handleClick = () => {
    inputRef?.current?.click();
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        const file_name = file.name;
        handleChangeImg(result);
        setImg(result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleChangeImg = async (img: string) => {
    setLoading(true);
    let res: any = await uploadImage({
      base64: img,
      name: `avatar_${Date.now()}`,
    }).finally(() => setLoading(false));

    if (res.code === 0) {
      onChange && onChange(res.data.url);
    }
  };

  useEffect(() => {
    let timer: any;
    if (loading) {
      timer = setInterval(() => {
        setLoadingText((state) => {
          let s = state + ".";
          if (s.length === 4) {
            return "";
          }
          return s;
        });
      }, 500);
    }

    return () => {
      clearInterval(timer);
    };
  }, [loading]);
  return (
    <div
      className="relative w-20 h-20 rounded-full bg-gray-200 mr-4 bg-center bg-cover border border-gray-300"
      style={{ backgroundImage: `url(${img || avatar})` }}
    >
      {/* 上传按钮 */}
      <div
        className={`absolute -bottom-1 -right-3  bg-blue-500 bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center ${
          loading ? "cursor-wait" : "cursor-pointer"
        }`}
        onClick={loading ? () => {} : handleClick}
      >
        <svg
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="1518"
          width="16"
          height="16"
        >
          <path
            d="M224 779.712l-0.128-439.712h146.976l45.856-106.048c1.856-4.256 10.496-9.952 15.136-9.952h192.288c4.768 0 13.248 5.6 15.136 9.92l45.856 106.08 146.88 0.32 0.032 439.68L224 779.68zM832.032 276h-104.832l-29.184-67.52C686.016 180.896 654.272 160 624.128 160h-192.288c-30.144 0-61.888 20.864-73.856 48.544l-29.184 67.456H223.968A64.224 64.224 0 0 0 160 340.32v439.36c0 35.488 28.672 64.32 63.968 64.32h608.064A64.192 64.192 0 0 0 896 779.68v-439.36c0-35.456-28.704-64.32-63.968-64.32z"
            fill="#ffffff"
            p-id="1519"
          ></path>
          <path
            d="M528 640c-52.928 0-96-43.072-96-96s43.072-96 96-96 96 43.072 96 96-43.072 96-96 96m0-256c-88.224 0-160 71.776-160 160s71.776 160 160 160 160-71.776 160-160-71.776-160-160-160"
            fill="#ffffff"
            p-id="1520"
          ></path>
        </svg>
      </div>

      {loading && (
        <div className="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-black/50 bg-opacity-50 rounded-full animation-pulse text-white text-3xl">
          {loadingText}
        </div>
      )}

      <input
        type="file"
        ref={inputRef}
        className="hidden"
        accept="image/*"
        onChange={handleFileChange}
      />
    </div>
  );
};

export default Index;
