"use client";

import { useEffect, useState } from "react";
import Spinner from "@/components/spinner";
import message from "@/components/message";
import { send_verify_code, resetPassword } from "@/services/user";
type IProps = {
  email: string;
  onFinish: () => void;
};

const Index = (props: IProps) => {
  const { email, onFinish } = props;
  const [loading, setLoading] = useState(false);
  const [code, setCode] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const handleSubmit = async () => {
    let cPattern = /^[0-9]{6}$/; // 6 digits
    let pPattern = /^.*(?=.{8,})(?=.*\d)(?=.*[A-Za-z]).*$/; // 8 characters, at least one number and one letter
    if (!cPattern.test(code)) {
      message.error("Code must be 6 digits");
      return;
    }
    if (!pPattern.test(password)) {
      message.error(
        "Password must be at least 8 characters, at least one number and one letter"
      );
      return;
    }
    if (password !== confirmPassword) {
      message.error("Passwords do not match");
      return;
    }
    setLoading(true);

    let res: any = await resetPassword({ email, code, password }).finally(() =>
      setLoading(false)
    );
    if (res.status === "success") {
      message.success("Password reset successfully");
      onFinish && onFinish();
    }
  };

  useEffect(() => {
    handleSendCode();
  }, []);

  const handleSendCode = async () => {
    if (!email) return message.error("Email is required");
    let res: any = await send_verify_code(email);
    if (res.status === "success") {
      message.success("Please check your email to verify your account");
    }
  };
  return (
    <div className="flex flex-col gap-6 w-[400px]">
      <div className="text-gray-500 text-lg">
        Verification code has been sent to{" "}
        <span className="font-bold text-gray-800">{email}</span>
      </div>
      <div className="border border-gray-200 px-4 py-2 rounded-md focus-within:border-blue-500 ">
        <input
          type="text"
          className="outline-none w-full text-xl"
          placeholder="Verification Code"
          value={code}
          onChange={(e) => setCode(e.target.value)}
        />
      </div>
      <div className="border border-gray-200  px-4 py-2 rounded-md focus-within:border-blue-500">
        <input
          type="password"
          className="outline-none w-full text-xl"
          placeholder="New Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
      </div>
      <div className="border border-gray-200  px-4 py-2 rounded-md focus-within:border-blue-500">
        <input
          type="password"
          className="outline-none w-full text-xl"
          placeholder="Confirm New Password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
        />
      </div>
      <div className="flex items-center gap-2">
        <button
          className={`  text-white rounded-xl flex items-center justify-center gap-2
            ${
              loading
                ? "cursor-wait bg-blue-400"
                : "cursor-pointer bg-blue-500 hover:bg-blue-700"
            } 
            text-lg px-6 py-2`}
          onClick={loading ? () => {} : handleSubmit}
        >
          {loading && <Spinner />}
          <span>{loading ? "loading..." : "Submit"}</span>
        </button>

        <div className="text-gray-500 text-lg">
          not received code?
          <button
            className="text-blue-500 hover:text-blue-700 cursor-pointer ml-2"
            onClick={handleSendCode}
          >
            resend
          </button>
        </div>
      </div>
    </div>
  );
};

export default Index;
