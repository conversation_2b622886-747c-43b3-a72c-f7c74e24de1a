"use client";

import { useEffect, useState } from "react";
import Spinner from "@/components/spinner";
import { useUserStore } from "@/providers/user-store-provider";
import { updateUserInfo } from "@/services/user";
import message from "@/components/message";
import Avatar from "./upload_avatar";
import Modal from "@/components/modal";
import ResetPasswordForm from "./resetPasswordForm";
import Link from "next/link";
const Index = () => {
  const {
    user_id,
    user_name,
    email: user_email,
    avatar,
    isLogin,
    tokens,
    freeze_tokens,
  } = useUserStore((state) => state);
  const [loading, setLoading] = useState(false);
  const [fullName, setFullName] = useState(user_name);
  const [headIcon, setHeadIcon] = useState(avatar);
  const [email, setEmail] = useState(user_email);
  const [showModal, setShowModal] = useState(false);
  const handleSubmit = async () => {
    if (!isLogin) {
      message.error("please login");
      return;
    }

    if (!fullName) {
      message.error("please enter your name");
      return;
    }
    setLoading(true);
    let res: any = await updateUserInfo({
      user_id,
      name: fullName,
      avatar: headIcon,
    });
    if (res.status === "success") {
      message.success("update success");
      window.location.reload();
    }
    setLoading(false);
  };

  const handleChangeHeadIcon = async (url: string) => {
    setHeadIcon(url);
  };

  const handeShowPassword = () => {
    setShowModal(true);
  };

  useEffect(() => {
    if (isLogin) {
      setFullName(user_name);
      setEmail(user_email);
      setHeadIcon(avatar);
    }
  }, [isLogin]);
  return (
    <div className="text-lg max-w-2xl">
      {/* 头像 */}
      <div className="flex items-center mb-4">
        <Avatar onChange={handleChangeHeadIcon} avatar={headIcon} />
        <div className="flex flex-col gap-1">
          <div className="font-bold">{fullName || "Your Name"}</div>
          <div className="text-gray-500">{email}</div>
        </div>
      </div>

      <div className="mt-8 flex items-center">
        <div className="text-xl text-gray-700">Tokens:</div>
        <div className="ml-2 text-2xl font-bold text-blue-500">{tokens}</div>
        <Link
          href={"/pricing"}
          className="text-lg cursor-pointer ml-10 bg-gray-100 rounded-md px-4 py-2 hover:bg-blue-400 hover:text-white"
        >
          Subcribe Plan
        </Link>
      </div>

      <div className="mt-8">
        <div className="text-xl text-gray-700">Full Name</div>
      </div>
      <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
        <input
          placeholder="please enter your name"
          className="w-full outline-none"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
        />
      </div>

      <div className="mt-8">
        <div className="text-xl text-gray-700">Email Address</div>
      </div>
      <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500 bg-gray-100 text-gray-500">
        <input
          disabled
          readOnly
          placeholder="please enter email address"
          className="w-full outline-none cursor-not-allowed"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
      </div>

      <div className="mt-8 flex">
        <div className="text-xl text-gray-700">Password</div>
        <div
          className="text-xl text-blue-500 cursor-pointer ml-4"
          onClick={handeShowPassword}
        >
          Reset Password
        </div>
      </div>

      <div className="mt-10 flex items-center">
        <button
          className={`  text-white rounded-2xl flex items-center justify-center gap-2
            ${
              loading
                ? "cursor-wait bg-blue-400"
                : "cursor-pointer bg-blue-500 hover:bg-blue-700"
            } 
            text-lg w-64 h-11`}
          onClick={loading ? () => {} : handleSubmit}
        >
          {loading && <Spinner />}
          <span>{loading ? "loading..." : "Update Profile"}</span>
        </button>
      </div>

      {email && (
        <Modal
          title="Reset Password"
          onClose={() => {
            setShowModal(false);
          }}
          visible={showModal}
        >
          <ResetPasswordForm
            email={email}
            onFinish={() => setShowModal(false)}
          />
        </Modal>
      )}
    </div>
  );
};

export default Index;
