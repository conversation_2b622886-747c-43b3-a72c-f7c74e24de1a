"use client";
import { uploadImageWithProcess } from "@/services/plan";
import { cancelRequest } from "@/services/request";
import { useEffect, useState } from "react";

type IProps = {
  base64: string;
  file_name: string;
  onUpload: (url?: string, file_name?: string) => void;
  onCancel: (file_name: string) => void;
  onDelete: (file_name: string) => void;
};

const Index = (props: IProps) => {
  const { base64, file_name, onUpload, onCancel, onDelete } = props;
  const [progress, setProgress] = useState(0);
  useEffect(() => {
    if (base64 && file_name) {
      handleUpload(base64, file_name);
    }
  }, []);

  // 取消上传
  const handleCancel = () => {
    cancelRequest();
    onCancel(file_name);
  };

  const handleDelete = () => {
    onDelete(file_name);
  };
  const handleUpload = async (base64: string, name: string) => {
    const data = {
      base64,
      name,
    };
    // 开始上传
    setStatus("uploading");
    let result: any = await uploadImageWithProcess(data, handleProcess);

    onUpload(result.data.url, name);
    // 上传完成
    setStatus("result");
    setProgress(0);
  };

  const handleProcess = (e: ProgressEvent) => {
    const { loaded, total } = e;
    let precent = Math.floor((loaded / total) * 100);
    setProgress(precent);
  };
  // preview image status
  const [status, setStatus] = useState<"uploading" | "result" | "empty">(
    "uploading"
  );

  return (
    <div className="w-48 h-48">
      <div className="relative w-full h-full p-2 border border-gray-300 rounded-md group">
        <img src={base64} alt="" className="w-full h-full" />

        {status === "uploading" && (
          <div className="absolute inset-0 bg-gray-300/30">
            <div
              className="bg-blue-400/30 h-full"
              style={{ width: `${progress}%` }}
            ></div>
            <div className="text-white text-lg absolute top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%]">
              {progress} %
            </div>
            <div
              className="absolute bottom-4 left-1/2 translate-x-[-50%] px-4 py-0.5 text-red-400 cursor-pointer text-lg rounded-xl bg-red-100 hover:bg-red-500 hover:text-white active:bg-red-700 select-none"
              onClick={handleCancel}
            >
              cancel
            </div>
          </div>
        )}
        {/* 上传完成 */}
        {status === "result" && (
          <div
            className="absolute -top-3 -right-3 p-1 cursor-pointer border rounded-full bg-white text-gray-500 group-hover:block hidden hover:text-blue-500 hover:border-blue-500"
            onClick={handleDelete}
          >
            <svg
              viewBox="64 64 896 896"
              focusable="false"
              data-icon="close"
              width="1em"
              height="1em"
              fill="currentColor"
            >
              <path d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"></path>
            </svg>
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;
