"use client";

import Upload from "@/components/upload";
import Preview from "./preview";
import { useRef, useState } from "react";
import message from "@/components/message";
import { createPlan } from "@/services/plan";
import { createPlan as createPlan2 } from "@/services/user";
import Spinner from "@/components/spinner";
type File = {
  base64: string;
  file_name: string;
};

type UploadImage = {
  url: string;
  file_name: string;
};

const Index = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [imgs, setImgs] = useState<UploadImage[]>([]);
  const [loading, setLoading] = useState(false);
  const iptRef = useRef<HTMLInputElement>(null);
  // 解析上传图片
  const handleUpladImage = (base64: string, file_name: string) => {
    files.push({ base64, file_name });
    setFiles([...files]);
  };

  // 上传成功
  const handleUpload = (url?: string, file_name?: string) => {
    if (url && file_name) {
      imgs.push({ url, file_name });
    }
  };

  // 取消上传
  const handleCancel = (file_name: string) => {
    setFiles(files.filter((item) => item.file_name !== file_name));
    setImgs(imgs.filter((item) => item.file_name !== file_name));
    handleRemoveInputValue();
  };

  // 删除已上传的图片
  const handleDelete = (file_name: string) => {
    setFiles(files.filter((item) => item.file_name !== file_name));
    setImgs(imgs.filter((item) => item.file_name !== file_name));
    handleRemoveInputValue();
  };

  // 移除隐藏input file 的值
  const handleRemoveInputValue = () => {
    iptRef.current!.value = "";
  };

  const handleSubmit = async () => {
    let token = localStorage.getItem("invite_code");
    let user_id = localStorage.getItem("user_id");
    if (!token) {
      message.error("please login");
      return;
    }
    let imageUrl = imgs[0]?.url;
    if (!imageUrl) {
      message.error("please upload image");
      return;
    }
    let data = {
      name: `plan_${Date.now()}`,
      user_id,
      images: {
        cloth: imageUrl,
      },
    };

    setLoading(true);
    const res: any = await createPlan(data);
    if (res.code === 0) {
      message.success("submit success");
    } else {
      message.error("submit fail");
      console.error(res.mesg);
    }
    setLoading(false);
    // navigate(`/plan/${res.plan_id}`);
  };

  const handleSubmit2 = async () => {
    const token = localStorage.getItem("token");
    let imageUrl = imgs[0]?.url;
    if (!imageUrl) {
      message.error("please upload image");
      return;
    }

    setLoading(true);
    const res = await createPlan2({ cloth: imageUrl });
    console.log(res);
  };

  return (
    <div className="w-full">
      <div>
        <div>
          <span className="text-2xl text-gray-700 mt-4">Industry</span>
          {/* 单选按钮 */}
          <div className="mt-4 flex flex-wrap gap-4 items-center">
            <div className=" bg-blue-500 text-white rounded-full px-6 py-2 text-xl cursor-pointer">
              CLOTHING
            </div>
            <div className=" bg-gray-200 rounded-full px-6 py-2 text-xl cursor-not-allowed">
              AUTOMOTIVE
            </div>
          </div>
        </div>
        <div className="text-2xl text-gray-700 mt-4">Enter your prompt</div>
        <div className="py-2 px-3.5 border border-gray-300 rounded-md mt-4 focus-within:border-[#3d86ef]">
          <textarea
            className="outline-none w-full text-xl"
            id="textarea1"
            placeholder="Describe your image in detail"
            rows={4}
          ></textarea>
        </div>
      </div>
      <div className="mt-10">
        <div className="text-2xl text-gray-700">
          <div>Upload Image</div>
          <span className="text-sm text-gray-500">
            need upload cloth image for generate
          </span>
        </div>
        <div className="mt-4 flex gap-4 flex-wrap">
          {files.map((item, index) => {
            return (
              <Preview
                key={index}
                base64={item.base64}
                file_name={item.file_name}
                onUpload={handleUpload}
                onCancel={handleCancel}
                onDelete={handleDelete}
              ></Preview>
            );
          })}

          {files.length > 0 ? null : (
            <Upload onImageUpload={handleUpladImage} inputRef={iptRef} />
          )}
        </div>
      </div>
      <div className="mt-10">
        <button
          className={`  text-white rounded-2xl flex items-center justify-center gap-2
            ${
              loading
                ? "cursor-wait bg-blue-400"
                : "cursor-pointer bg-blue-500 hover:bg-blue-700"
            } 
            text-lg w-48 h-11`}
          onClick={loading ? () => {} : handleSubmit2}
        >
          {loading && <Spinner />}
          <span>{loading ? "loading..." : "Submit"}</span>
        </button>
      </div>
    </div>
  );
};

export default Index;
