const Index = ({ step, status }: { step: number; status: number }) => {
  let bgColor = "";
  let text = "";

  switch (step) {
    case -1:
      bgColor = "bg-gray-500";
      text = "empty plan";
      break;
    case 1:
      bgColor = "bg-yellow-500";
      text = "image generation";
      break;
    case 2:
      bgColor = "bg-orange-500";
      text = "script generation";
      break;
    case 3:
      bgColor = "bg-green-500";
      text = "frame generation";
      break;
    case 4:
      bgColor = "bg-blue-500";
      text = "storyboard generation";
      break;
    case 5:
      bgColor = "bg-purple-500";
      text = "";
  }

  return (
    <span className={`${bgColor} text-white text-xs px-2 py-1 rounded-full`}>
      {step != -1 ? text + (status == 1 ? "ing" : " completed") : "empty plan"}
    </span>
  );
};

export default Index;
