import StatusBadge from "./statsBadge";

// 格式化日期
const formatDate = (dateString: number | string) => {
  if (!dateString)
    return new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// 网格视图的视频卡片
const Index = (props: any) => {
  const { video } = props;
  return (
    <div className="overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-[#00f0b5]/20 cursor-pointer">
      <div
        className={`relative aspect-video thumbnail-bg bg-center bg-contain bg-no-repeat border-b border-gray-200`}
        style={{ backgroundImage: `url(${video.cover_image})` }}
      >
        <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 text-xs rounded z-20">
          {video.duration || "00:00"}
        </div>
        <div className="absolute top-2 right-2 z-20">
          <StatusBadge step={video.step} status={video.status} />
        </div>
      </div>
      <div className="px-6 py-4 bg-gray-50">
        <h3 className="text-lg font-semibold text-black mb-2 truncate">
          {video.name}
        </h3>

        <div className="flex justify-between items-center text-xs text-gray-400">
          <span>{formatDate(video.create_time)}</span>
          <div className="flex space-x-3">
            <span className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              {video.views}
            </span>
            <span className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
              {video.likes}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
