"use client";

import { getPlanList } from "@/services/plan";
import Link from "next/link";
import { useEffect, useState } from "react";
import Card from "./card";
const Index = () => {
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  useEffect(() => {
    handleGetData();
  }, []);

  const handleGetData = async () => {
    const user_id = localStorage.getItem("user_id");
    setLoading(true);
    const result: any = await getPlanList(user_id).finally(() =>
      setLoading(false)
    );

    if (result.code === 0) {
      setData(result.data.plans);
    }
  };
  return (
    <>
      {loading ? (
        <div>loading...</div>
      ) : data?.length === 0 ? (
        <div className="flex mt-4 h-64 items-center justify-center bg-gray-200 rounded-2xl text-lg">
          <span>Generate Video will appear here,</span>
          <Link href="/generate" className="text-blue-500 ml-2 font-bold">
            Starting creating!
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 gap-6">
          {data.map((item: any, index: number) => {
            return (
              <Link href={`/product?plan_id=${item.plan_id}`} key={index}>
                <div className="bg-white rounded-lg shadow-md overflow-hidden flex-1 flex flex-col border border-gray-200">
                  <Card video={item}></Card>
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </>
  );
};

export default Index;
