"use client";
import Header from "./header";
import ScriptInfo from "./scriptInfo";
import FrameInfo from "./frameInfo";
import VideoInfo from "./video";
import StoryBoard from "./storyBoard";
import Step from "./step";
import { useSearchParams } from "next/navigation";
import { getPlanDetail } from "@/services/plan";
import { useEffect, useState } from "react";
const Index = () => {
  const params = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [plan, setPlan] = useState<any>(null);

  useEffect(() => {
    if (plan) {
      setLoading(false);
    } else {
      setLoading(true);
    }
  }, [plan]);

  useEffect(() => {
    handleGetData();
  }, []);

  const handleGetData = async () => {
    let plan = await handleGetDetail();

    let step = plan?.step;
    let status = plan?.status;
    if (step === 5 && status === 2) {
      return;
    }
    setTimeout(() => {
      handleGetData();
    }, 2000);
  };

  const handleGetDetail = async () => {
    const planId = params.get("plan_id");
    if (!planId) return;
    const res: any = await getPlanDetail(planId);
    setPlan(res.data.plan);
    return res.data.plan;
  };

  const [sences, setSences] = useState<number>(0);

  const scriptLoading = loading || !plan?.input_analyse;
  const sencesLoading = loading || !plan?.scenes;
  return (
    <>
      <Header>
        <div className="flex-1">
          <Step currentStep={plan?.step} status={plan?.status}></Step>
        </div>
      </Header>
      <div className="mt-27 flex-1 grid grid-cols-12 grid-rows-12 gap-4 w-full p-4">
        <div className="rounded-lg p-4 col-span-3 row-span-9 bg-white flex flex-col">
          <ScriptInfo content={plan?.input_analyse} loading={scriptLoading} />
        </div>
        <div className="rounded-lg p-4 col-span-9 row-span-9 bg-white flex flex-col">
          <VideoInfo videos={plan?.clip_videos} />
        </div>
        <div className="rounded-lg p-4 col-span-3 row-span-3 bg-white">
          <FrameInfo
            sences={sences}
            info={plan?.scenes}
            loading={sencesLoading}
          />
        </div>
        <div className="rounded-lg p-4 col-span-9 row-span-3 bg-white flex flex-col">
          <StoryBoard
            sences={sences}
            setSences={setSences}
            frameImages={plan?.clip_frames}
          />
        </div>
      </div>
    </>
  );
};

export default Index;
