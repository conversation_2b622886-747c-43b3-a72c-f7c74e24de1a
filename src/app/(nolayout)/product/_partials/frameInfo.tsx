import { useState } from "react";
import Title from "./title";

type IPrps = {
  sences: number; //场景数
  info: any;
  loading: boolean;
};

const Index = (props: IPrps) => {
  const { sences, info = [], loading } = props;
  // const [info, setInfo] = useState<any>([
  //   {
  //     sequence: "1",
  //     description: "明亮的室内空间，白色极简风格背景",
  //     actions: "模特从远处缓步走来，展现服装整体轮廓",
  //     shot_type: "全景推进",
  //   },
  //   {
  //     sequence: "2",
  //     description: "同一场景，自然光从侧面打入",
  //     actions: "模特优雅转身，重点展示双排扣和翻领设计",
  //     shot_type: "中景侧面拍摄",
  //   },
  //   {
  //     sequence: "3",
  //     description: "同一场景，光线柔和",
  //     actions: "特写模特腰部，展示褶皱设计和收腰细节",
  //     shot_type: "特写镜头",
  //   },
  //   {
  //     sequence: "4",
  //     description: "同一场景，打入柔光",
  //     actions: "模特轻轻转圈，展示裙摆的飘逸感和A字型廓形",
  //     shot_type: "中远景跟拍",
  //   },
  //   {
  //     sequence: "5",
  //     description: "同一场景，光影交错",
  //     actions: "模特微笑回眸，展示整体造型与气质",
  //     shot_type: "中景正面特写",
  //   },
  //   {
  //     sequence: "6",
  //     description: "同一场景，柔和逆光",
  //     actions: "模特从容走出画面，展示背影和裙摆摆动",
  //     shot_type: "全景慢动作",
  //   },
  // ]);
  return (
    <>
      <Title>Storyboard Info</Title>
      <div className="text-xl">
        {info.map((item: any, index: number) => {
          return (
            <div
              key={item.sequence}
              className={`${sences === index ? "block" : "hidden"}`}
            >
              <div className="flex items-center">
                <div className="text-lg mr-2 text-blue-500 bg-blue-100 rounded-md h-8 w-8 flex items-center justify-center">
                  {item.sequence}
                </div>
                <div className="text-2xl">{item.shot_type}</div>
              </div>

              <div className="mt-4 flex flex-col gap-2 bg-gray-100 px-6 py-4 rounded-md">
                <div>{item.description}</div>
                <div>{item.actions}</div>
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default Index;
