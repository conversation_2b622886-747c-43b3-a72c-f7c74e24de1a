"use client";
import { useRouter } from "next/navigation";

const Index = (props: any) => {
  const { children } = props;
  const router = useRouter();
  const handleBack = () => {
    router.back();
  };
  return (
    <div className="flex justify-between items-center w-full min-w-7xl h-max bg-white px-4 py-2 gap-4 absolute top-0 left-0">
      <div
        className="flex items-center gap-3 cursor-pointer hover:text-blue-500"
        onClick={handleBack}
      >
        <svg
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="8587"
          width="16"
          height="16"
          fill="currentColor"
        >
          <path
            d="M711.841477 976.738462l88.300308-86.171569L404.393354 513.969231 800.141785 137.371569 711.841477 51.2 227.796677 513.969231 711.841477 976.738462z"
            p-id="8588"
          ></path>
        </svg>

        <div className="">
          <span className="text-2xl">back</span>
        </div>
        {/* TODO:修改product name */}
        {/* <div className="text-gray-600 text-2xl focus-within:border-blue-500 p-2 w-max border-b border-transparent">
          <input
            type="text"
            defaultValue={"plan name"}
            className="bg-transparent outline-none w-[130px]"
          />
        </div> */}
      </div>
      {children}
      <div className="w-20">{/* 占位 */}</div>
    </div>
  );
};

export default Index;
