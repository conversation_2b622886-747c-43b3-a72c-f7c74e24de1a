"use client";

import { useEffect, useRef, useState } from "react";
import Title from "./title";

type IProps = {
  content: string;
  loading: boolean;
};

const Content = `让我从专业时尚角度分析这套休闲运动风格的搭配:

色彩：
- 主色调采用浅灰色和浅牛仔蓝的清新搭配
- 色彩组合简约自然，体现出年轻活力的休闲风格
- 灰色系与牛仔蓝的搭配和谐统一

剪裁：
- 上衣采用修身的运动风格剪裁，展现出利落的线条
- 外套选用宽松版型，营造出随性慵懒的效果
- 高腰牛仔裤的剪裁突出腰线，拉长身材比例

面料：
- 运动背心采用棉质针织面料,贴身舒适
- 外套使用棉质卫衣面料,柔软保暖
- 牛仔裤选用中等厚度的牛仔布,挺括有型

版型：
- 上衣采用贴身的运动背心款式
- 外套为宽松版型的卫衣设计
- 牛仔裤为高腰直筒版型

服装风格：
- 整体风格偏向运动休闲
- 融合了街头时尚与运动元素
- 展现出青春活力的都市休闲风

细节装饰：
- 运动背心的圆领设计简约大方
- 外套采用基础款式,突出实用性
- 牛仔裤的口袋和车缝线条清晰

这套搭配整体简约时尚,非常适合日常休闲场合穿着。`;

const Index = (props: IProps) => {
  const { content, loading } = props;
  const [scroll, setScroll] = useState(0);
  const [scrollBottom, setScrollBottom] = useState(0);
  const preRef = useRef<HTMLPreElement>(null);
  useEffect(() => {
    // 设置高度
    setScroll(preRef.current?.scrollTop!);
    setScrollBottom(
      preRef.current?.scrollHeight! - preRef.current?.clientHeight!
    );
  }, [preRef]);

  return (
    <>
      <Title>Image Analysis</Title>
      {loading ? (
        <div>loading...</div>
      ) : (
        <pre
          ref={preRef}
          onScroll={(e) => {
            const target = e.target as HTMLElement;
            setScroll(target.scrollTop);
          }}
          className={`flex-1 overflow-auto text-lg p-2 whitespace-pre-wrap text-gray-700 ${
            scroll > 0 ? "mask-t-from-90%" : ""
          } ${scroll < scrollBottom ? "mask-b-from-90%" : ""}`}
        >
          {content}
        </pre>
      )}
    </>
  );
};

export default Index;
