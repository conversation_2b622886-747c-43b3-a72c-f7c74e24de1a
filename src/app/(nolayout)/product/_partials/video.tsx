"use client";

import { AbsoluteFill, Audio, OffthreadVideo, Sequence } from "remotion";
import Title from "./title";
import { Player } from "@remotion/player";
import { parseMedia } from "@remotion/media-parser";
import { useEffect, useState } from "react";

type IProps = {
  videos: any[];
};
const FPS = 60;

const Videos = (props: any) => {
  const { videos, audio, totalInFrames } = props;
  return (
    <>
      {videos.map((video: any, index: number) => {
        const duration = video.durationInSeconds;
        return (
          <Sequence
            durationInFrames={duration * FPS}
            key={index}
            from={index * FPS * duration}
          >
            <OffthreadVideo className="w-full h-full" src={video.src} />
          </Sequence>
        );
      })}
      {audio && (
        <Sequence durationInFrames={totalInFrames}>
          <Audio src={audio} />
        </Sequence>
      )}
    </>
  );
};

const Index = (props: IProps) => {
  const { videos } = props;
  const [videoObj, setVideoObj] = useState<any>([]);
  const [durationInFrames, setDurationInFrames] = useState<any>(FPS);

  useEffect(() => {
    if (videos?.length > 0) {
      parseVideos();
    }
  }, [videos]);

  // 获取videos meta数据
  const parseVideos = async () => {
    const result = [];
    let totalInFrames = 0;
    for (let src of videos) {
      const parsed = await parseMedia({
        src,
        fields: {
          durationInSeconds: true,
          dimensions: true,
        },
      });
      let durationInSeconds = Math.floor(parsed.durationInSeconds || 0);
      totalInFrames += durationInSeconds * FPS;
      result.push({
        src,
        dimensions: parsed.dimensions,
        durationInSeconds: durationInSeconds,
      });
    }
    setDurationInFrames(Math.floor(totalInFrames));
    setVideoObj(result);
  };

  return (
    <>
      <Title>Final Video</Title>
      <div className="flex-1 flex items-center justify-center bg-black rounded-xl overflow-hidden">
        {videos?.length > 0 ? (
          <Player
            component={Videos}
            inputProps={{
              videos: videoObj,
              totalInFrames: durationInFrames,
            }}
            durationInFrames={durationInFrames}
            fps={FPS}
            compositionWidth={300}
            compositionHeight={300}
            style={{
              width: "100%",
              height: "100%",
            }}
            controls
          />
        ) : (
          // TODO:loading
          <div className="text-white text-2xl">
            ✨ Creating your masterpiece...
          </div>
        )}
      </div>
    </>
  );
};

export default Index;
