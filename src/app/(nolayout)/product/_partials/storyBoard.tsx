import { useState } from "react";
import Title from "./title";

type IPrps = {
  sences: number; //场景数
  setSences: (sences: number) => void;
  frameImages: string[]; //场景图片
};

const Index = (props: IPrps) => {
  const { sences, setSences, frameImages = [] } = props;

  return (
    <>
      <Title>StoryBoard Video</Title>
      <div className="flex gap-2 items-center p-2 rounded-md flex-1 overflow-hidden">
        {frameImages.map((img: any, index: number) => {
          return (
            <div
              key={index}
              className={`flex-1 h-full bg-black rounded-md overflow-hidden flex justify-center ${
                sences === index ? "ring-3 ring-blue-500" : ""
              }`}
              onMouseEnter={() => setSences(index)}
            >
              <img className="h-full" src={img}></img>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default Index;
