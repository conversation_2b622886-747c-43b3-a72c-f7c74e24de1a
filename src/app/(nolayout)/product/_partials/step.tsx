type IProps = {
  currentStep: number;
  status: number;
};

const Steps = [
  {
    id: "imageAnalysis",
    title: "image analysis",
    step: 1,
  },
  {
    id: "scriptGeneration",
    title: "script generation",
    step: 2,
  },
  {
    id: "firstFrame",
    title: "first frame",
    step: 3,
  },
  {
    id: "storyboardShooting",
    title: "storyboard shooting",
    step: 4,
  },
  {
    id: "videoComposition",
    title: "video composition",
    step: 5,
  },
];

const Index = (props: IProps) => {
  const getProcessing = (step: number) => {
    // 进行中的
    if (step === props.currentStep && props.status === 1) {
      return true;
    }

    if (step - 1 === props.currentStep && props.status === 2) {
      return true;
    }

    if (step === 1 && props.currentStep === -1) {
      return true;
    }

    return false;
  };

  const getCompleted = (step: number) => {
    // 已完成
    if (step < props.currentStep) {
      return true;
    }

    if (step === props.currentStep && props.status === 2) {
      return true;
    }

    return false;
  };

  return (
    <div className="flex justify-center items-center relative gap-12 px-4 py-2 w-full">
      {Steps.map((item, index) => {
        const currentStep = item.step;
        // 已完成
        const isCompleted = getCompleted(currentStep);
        // 进行中
        const isProcessing = getProcessing(currentStep);

        return (
          <div
            key={item.id}
            className={`flex flex-col items-center relative z-10 ${
              isProcessing ? "animate-pulse" : ""
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                isProcessing
                  ? "border-blue-500 text-blue-500"
                  : isCompleted
                  ? "border-green-500 bg-green-500 text-white"
                  : "border-gray-400 text-gray-400"
              }`}
            >
              {isCompleted ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              ) : isProcessing ? (
                <svg
                  className="h-5 w-5 animate-spin"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="1455"
                  width="32"
                  height="32"
                  fill="currentColor"
                >
                  <path
                    d="M511.997366 117.728395a392.928395 392.928395 0 0 1 233.797531 76.90535l-107.693827 15.27572a39.506173 39.506173 0 0 0 5.530864 78.485597 40.586008 40.586008 0 0 0 5.636214-0.263375l184.36214-26.337448a39.348148 39.348148 0 0 0 33.527572-44.510288l-26.337448-183.835391a39.506173 39.506173 0 0 0-78.222223 11.061728l10.53498 72.954733A472.678189 472.678189 0 0 0 62.575144 661.069959l64-64A393.112757 393.112757 0 0 1 511.997366 117.728395z m449.474897 244.674897l-64 64A394.482305 394.482305 0 0 1 273.985844 825.679012l85.57037-12.115226a39.506173 39.506173 0 1 0-11.167078-78.222222l-184.36214 26.337448a39.506173 39.506173 0 0 0-33.527572 44.510288l26.337448 183.835391A39.506173 39.506173 0 0 0 195.947984 1024a41.08642 41.08642 0 0 0 5.636214-0.526749 39.242798 39.242798 0 0 0 33.527572-44.510288l-13.537449-94.55144a472.546502 472.546502 0 0 0 739.897942-522.008231z"
                    p-id="1456"
                  ></path>
                </svg>
              ) : (
                item.step
              )}
            </div>
            <div className="mt-2 text-center">
              <div
                className={`mt-1 ${
                  isProcessing
                    ? "text-blue-500"
                    : isCompleted
                    ? "text-green-500"
                    : "text-gray-400"
                }`}
              >
                {item.title}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Index;
