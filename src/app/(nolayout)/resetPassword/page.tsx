import CloseBtn from "./components/btn";
import ResetPassword from "./components/reset_password";
const Index = () => {
  return (
    <div className="h-screen w-screen overflow-hidden flex flex-col items-center justify-center relative ">
      {/* 背景色 */}
      <div className="absolute w-[800px] h-[800px] top-[-500px] left-1/3 translate-x-[-50%] rounded-full  bg-gradient-to-b from-white to-blue-500 animate-pulse"></div>
      <div className="absolute w-[900px] h-[900px] bottom-[-400px] left-[-400px] rounded-full bg-gradient-to-tr from-blue-200 to-green-500  animate-pulse"></div>
      <div className="absolute w-[500px] h-[500px] bottom-[-100px] right-[-100px] rounded-full bg-gradient-to-tl to-yellow-100 from-red-500  animate-pulse"></div>

      <div className="w-full h-full flex items-center justify-cente backdrop-blur-3xl">
        <div className="flex-1">
          <div className="mx-auto w-xl shadow-lg rounded-lg bg-gray-50/10 flex flex-col py-16 px-12 backdrop-blur-3xl relative z-10">
            <div className="text-3xl">Password Recovery</div>
            <div className="border-b border-gray-300 mt-6 animate-pulse"></div>

            <ResetPassword />

            {/* 关闭按钮 */}
            <CloseBtn />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
