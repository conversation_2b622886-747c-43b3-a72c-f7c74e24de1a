"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "@/providers/user-store-provider";
import Spinner from "@/components/spinner";
import message from "@/components/message";
import {
  register,
  resetPassword,
  send_verify_code,
  verify_email,
} from "@/services/user";
import Link from "next/link";
const Index = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showReset, setShowReset] = useState(false);
  const [verifyCode, setVerifyCode] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSendCode = async () => {
    if (!email) return message.error("Email is required");
    if (!/\S+@\S+\.\S+/.test(email)) {
      message.error("Please enter valid email");
      return;
    }

    const res: any = await send_verify_code(email);
    if (res.status === "success") {
      message.success("Code sent to your email");
      setShowReset(true);
    } else {
      message.error(res.message);
    }
  };

  const handleSubmit = async () => {
    if (!email) return message.error("Email is required");

    if (!password) return message.error("Password is required");
    if (password !== confirmPassword)
      return message.error("Password is not match");

    var pPattern = /^.*(?=.{8,})(?=.*\d)(?=.*[A-Za-z]).*$/;
    if (!pPattern.test(password)) {
      message.error(
        "Password must be at least 8 characters long and contain at least one number and one letter"
      );
      return;
    }
    let pattern = /^\d{6}$/;
    if (!pattern.test(verifyCode)) {
      message.error("Please enter valid verify code");
      return;
    }

    let res: any = await resetPassword({
      email,
      code: verifyCode,
      password,
    }).finally(() => setLoading(false));
    if (res.status === "success") {
      message.success("Password reset successfully");
      router.push("/login");
    } else {
      message.error(res.message);
    }
  };

  return (
    <>
      {showReset ? (
        <>
          <div className="mt-8">
            <div className="text-xl text-gray-700">Password</div>
          </div>
          <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
            <input
              type="password"
              placeholder="Enter your password"
              className="w-full outline-none"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          <div className="mt-8">
            <div className="text-xl text-gray-700">Confirm Password</div>
          </div>
          <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
            <input
              type="password"
              placeholder="Confirm password"
              className="w-full outline-none"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>
          <div className="mt-6">
            <div className="text-xl text-gray-700">Verify Code</div>
          </div>
          <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
            <input
              type="text"
              placeholder="Enter your verify code"
              className="w-full outline-none"
              value={verifyCode}
              onChange={(e) => setVerifyCode(e.target.value)}
            />
          </div>
          <div className="mt-8">
            <button
              className={`bg-blue-400 cursor-pointer hover:bg-blue-500 text-white px-4 py-3 rounded-lg w-full text-xl shadow-sm
                 ${
                   loading
                     ? "cursor-wait bg-blue-400"
                     : "cursor-pointer bg-blue-500 hover:bg-blue-700"
                 } 
                `}
              onClick={handleSubmit}
            >
              {loading && <Spinner />}
              <span>{loading ? "loading..." : "Submit"}</span>
            </button>
          </div>
        </>
      ) : (
        <>
          <div className="mt-6">
            <div className="text-xl text-gray-700">Email Address</div>
          </div>
          <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
            <input
              type="text"
              placeholder="Enter your email"
              className="w-full outline-none"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <div className="mt-8">
            <button
              className="bg-blue-400 cursor-pointer hover:bg-blue-500 text-white px-4 py-3 rounded-lg w-full text-xl shadow-sm"
              onClick={handleSendCode}
            >
              Send Code
            </button>
          </div>
          <div className="mt-8 text-center text-gray-500">
            <span>Already have an account? </span>
            <Link href="/login" className="text-blue-500">
              Sign In
            </Link>
          </div>
        </>
      )}
    </>
  );
};

export default Index;
