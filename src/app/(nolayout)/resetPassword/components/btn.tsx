"use client";

import { useRouter } from "next/navigation";

const Index = () => {
  const router = useRouter();
  const handleBack = () => {
    router.push("/login");
  };
  return (
    <div
      className="absolute top-0 right-0 p-4 cursor-pointer text-gray-400 hover:text-blue-600"
      onClick={handleBack}
    >
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="7434"
        width="18"
        height="18"
      >
        <path
          d="M585.28 517.376l339.392-339.392a48.32 48.32 0 0 0 0-67.84 47.872 47.872 0 0 0-67.84 0L517.312 449.536 177.984 110.08a48.32 48.32 0 0 0-67.84 0 47.872 47.872 0 0 0 0 67.84l339.392 339.392-339.392 339.392a48.32 48.32 0 0 0 0 67.904 47.872 47.872 0 0 0 67.84 0l339.392-339.392 339.392 339.392a48.32 48.32 0 0 0 67.904 0 47.872 47.872 0 0 0 0-67.84L585.28 517.312z"
          fill="currentColor"
          p-id="7435"
        ></path>
      </svg>
    </div>
  );
};

export default Index;
