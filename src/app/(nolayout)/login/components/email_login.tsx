"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "@/providers/user-store-provider";
import { login } from "@/services/user";
import message from "@/components/message";
import Link from "next/link";
const Index = () => {
  const email_login = useUserStore((state) => state.email_login);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const router = useRouter();
  const handleLogin = async () => {
    if (!email) {
      message.error("Please enter email");
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      message.error("Please enter valid email");
      return;
    }

    let res: any = await login(email, password);

    if (res.status === "success") {
      const user = res.user;
      message.success("success login");
      email_login({
        email: user.email,
        user_name: user.user_name,
        user_id: user.id,
        avatar: user.avatar,
        isLogin: true,
      });
      localStorage.setItem("token", res.token);
      router.push("/");
    } else {
      message.error(res.message);
    }
  };
  return (
    <>
      <div className="mt-6">
        <div className="text-xl text-gray-700">Email Address</div>
      </div>
      <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
        <input
          type="text"
          placeholder="Enter your email"
          className="w-full outline-none"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
      </div>
      <div className="mt-8">
        <div className="text-xl text-gray-700 flex justify-between">
          <span>Password</span>
          <Link href={"/resetPassword"} className="text-blue-500">
            Forget Password?
          </Link>
        </div>
      </div>
      <div className="border mt-2 border-gray-300 flex flex-col items-center p-3 text-xl rounded-lg focus-within:border-blue-500">
        <input
          type="password"
          placeholder="Enter your password"
          className="w-full outline-none"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
      </div>

      <div className="mt-8">
        <button
          className="bg-blue-400 cursor-pointer hover:bg-blue-500 text-white px-4 py-3 rounded-lg w-full text-xl shadow-sm"
          onClick={handleLogin}
        >
          Sign In
        </button>
      </div>
      <div className="mt-8 text-center text-gray-500">
        <span>Don't have an account? </span>
        <Link href="/register" className="text-blue-500">
          Sign up for free
        </Link>
      </div>
    </>
  );
};

export default Index;
