"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "@/providers/user-store-provider";
import message from "@/components/message";
const Index = () => {
  const [inviteCode, setInviteCode] = useState("");
  const router = useRouter();
  const { invite_login } = useUserStore((state) => state);
  const handleLogin = () => {
    if (!inviteCode) {
      message.error("Please enter invite code");
      return;
    }
    invite_login(inviteCode);
    message.success("success login");
    router.push("/");
  };
  return (
    <>
      <div className="mt-8">
        <div className="text-xl text-gray-500">Invite Code</div>
      </div>
      <div className="border mt-2 border-gray-300 flex flex-col items-center p-4 text-xl rounded-lg focus-within:border-blue-500">
        <input
          type="text"
          placeholder="please enter your invite code"
          className="w-full outline-none"
          value={inviteCode}
          onChange={(e) => setInviteCode(e.target.value)}
        />
      </div>
      <div className="mt-6">
        <button
          className="bg-blue-400 cursor-pointer hover:bg-blue-500 text-white px-4 py-4 rounded-lg w-full text-xl shadow-sm"
          onClick={handleLogin}
        >
          Submit
        </button>
      </div>
    </>
  );
};

export default Index;
