"use client";
import message from "@/components/message";
import { useEffect } from "react";
import { googleLogin } from "@/services/user";
import { useUserStore } from "@/providers/user-store-provider";
import { useRouter } from "next/navigation";
const Index = () => {
  const email_login = useUserStore((state) => state.email_login);
  const router = useRouter();
  function decodeJWT(token: string) {
    let base64Url = token.split(".")[1];
    let base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    let jsonPayload = decodeURIComponent(
      atob(base64)
        .split("")
        .map(function (c) {
          return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join("")
    );
    return JSON.parse(jsonPayload);
  }

  const handleGoogleLogin = async (googleUser: any) => {
    let data = decodeJWT(googleUser.credential);
    let res: any = await googleLogin(data);
    if (res.status === "success") {
      const user = res.user;
      message.success("success login");
      email_login({
        email: user.email,
        user_name: user.user_name,
        user_id: user.id,
        avatar: user.avatar,
        isLogin: true,
      });
      localStorage.setItem("token", res.token);
      router.push("/");
    }
  };

  useEffect(() => {
    load_script();
    (window as any)["onSignIn"] = handleGoogleLogin;
  }, []);

  //   加载谷歌脚本
  const load_script = () => {
    const script = document.createElement("script");
    script.id = "google-login-script";
    script.src = `https://accounts.google.com/gsi/client?hl=en`;
    script.async = true;
    script.defer = true;
    document.body.appendChild(script);
  };
  return (
    <>
      <div className="relative border border-gray-300 mt-8 flex items-center justify-center gap-3 p-3 text-lg rounded-lg cursor-pointer hover:bg-blue-100 shadow-sm">
        <svg
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="6303"
          width="14"
          height="14"
        >
          <path
            d="M214.101333 512c0-32.512 5.546667-63.701333 15.36-92.928L57.173333 290.218667A491.861333 491.861333 0 0 0 4.693333 512c0 79.701333 18.858667 154.88 52.394667 221.610667l172.202667-129.066667A290.56 290.56 0 0 1 214.101333 512"
            fill="#FBBC05"
            p-id="6304"
          ></path>
          <path
            d="M516.693333 216.192c72.106667 0 137.258667 25.002667 188.458667 65.962667L854.101333 136.533333C763.349333 59.178667 646.997333 11.392 516.693333 11.392c-202.325333 0-376.234667 113.28-459.52 278.826667l172.373334 128.853333c39.68-118.016 152.832-202.88 287.146666-202.88"
            fill="#EA4335"
            p-id="6305"
          ></path>
          <path
            d="M516.693333 807.808c-134.357333 0-247.509333-84.864-287.232-202.88l-172.288 128.853333c83.242667 165.546667 257.152 278.826667 459.52 278.826667 124.842667 0 244.053333-43.392 333.568-124.757333l-163.584-123.818667c-46.122667 28.458667-104.234667 43.776-170.026666 43.776"
            fill="#34A853"
            p-id="6306"
          ></path>
          <path
            d="M1005.397333 512c0-29.568-4.693333-61.44-11.648-91.008H516.650667V614.4h274.602666c-13.696 65.962667-51.072 116.650667-104.533333 149.632l163.541333 123.818667c93.994667-85.418667 155.136-212.650667 155.136-375.850667"
            fill="#4285F4"
            p-id="6307"
          ></path>
        </svg>
        <div className="">Join With Google</div>

        {/* google login 遮罩按钮 */}
        <div className="g_id_signin flex-1 absolute inset-0 opacity-0"></div>
        <div
          id="g_id_onload"
          data-client_id="389065590898-hu32c6pftatm8o6lmjoigu0phe12sdp3.apps.googleusercontent.com"
          data-callback="onSignIn"
        ></div>
      </div>
    </>
  );
};

export default Index;
