import CloseBtn from "./components/btn";
import GoogleBtn from "./components/google_btn";
import InviteLogin from "./components/invite_login";
import EmailLogin from "./components/email_login";
const Index = () => {
  return (
    <div className="h-screen w-screen overflow-hidden flex flex-col items-center justify-center relative ">
      {/* 背景色 */}
      <div className="absolute w-[800px] h-[800px] top-[-500px] left-1/3 translate-x-[-50%] rounded-full  bg-gradient-to-b from-white to-blue-500 animate-pulse"></div>
      <div className="absolute w-[900px] h-[900px] bottom-[-400px] left-[-400px] rounded-full bg-gradient-to-tr from-blue-200 to-green-500  animate-pulse"></div>
      <div className="absolute w-[500px] h-[500px] bottom-[-100px] right-[-100px] rounded-full bg-gradient-to-tl to-yellow-100 from-red-500  animate-pulse"></div>

      <div className="w-full h-full flex items-center justify-cente backdrop-blur-3xl">
        {/* backdrop-blur-3xl */}
        <div className="flex-1">
          <div className="mx-auto w-xl shadow-lg rounded-lg bg-gray-50/10 flex flex-col py-16 px-12 backdrop-blur-3xl relative z-10">
            <div className="text-3xl">Welcome to Dream Loom!</div>
            <div className="border-b border-gray-300 mt-6 animate-pulse"></div>
            {/* <InviteLogin /> */}
            <GoogleBtn />
            {/* 分割线 */}
            <div className="flex gap-6 items-center mt-6">
              <div className=" bg-gray-300 w-1/2 h-[1px]"></div>
              <span className="text-gray-500 text-xl">or</span>
              <div className=" bg-gray-300 w-1/2 h-[1px]"></div>
            </div>
            <EmailLogin />

            {/* 关闭按钮 */}
            <CloseBtn />
          </div>
        </div>

        <div className="absolute  h-[70px] w-[70px] rounded-lg shadow-xl top-[120px] left-1/2 -z-10"></div>
        <div className="absolute  h-[120px] w-[120px] rounded-full shadow-xl top-[220px] left-1/4 -z-10 animate-pulse"></div>
        <div className="absolute h-[80px] w-[80px] rounded-lg shadow-lg top-[400px] right-1/4 -z-10"></div>
        <div className="absolute h-[60px] w-[60px] rounded-lg shadow-lg bottom-[200px] right-2/7 -z-10"></div>
      </div>
    </div>
  );
};

export default Index;
