import { post, get } from "./request";

let url_nest = "http://localhost:9000";
// let url_nest = "/user";
/**
 * 用户注册
 * @param email 用户邮箱 例如 <EMAIL>
 * @param password 用户密码
 */
const register = (email: String, password: String) => {
  let url = `${url_nest}/register`;
  return post(url, { email, password });
};

/**
 * 发送验证码
 * @param email 用户邮箱 例如 <EMAIL>
 */
const send_verify_code = (email: String) => {
  let url = `${url_nest}/send_verify_code`;
  return post(url, { email });
};

/**
 * 邮箱验证
 * @param email 用户邮箱 例如 <EMAIL>
 * @param code 验证码
 */
const verify_email = (email: String, code: String) => {
  let url = `${url_nest}/verify_email`;
  return post(url, { email, code });
};

/**
 * 用户登录
 * @param email 用户邮箱 例如 <EMAIL>
 * @param password 用户密码
 */
const login = (email: String, password: String) => {
  let url = `${url_nest}/login`;
  return post(url, { email, password });
};

/**
 * 获取用户信息 根据token
 */
const getUserInfo = () => {
  let url = `${url_nest}/get_user_info`;
  return post(url);
};

/**
 * google 三方登录
 * @param email 用户邮箱 谷歌邮箱
 * @param name 用户名
 * @param picture 用户头像
 */
const googleLogin = ({ email, name, picture }: any) => {
  let url = `${url_nest}/google_login`;
  return post(url, { email, name, picture });
};

/**
 * 更新用户信息
 * @param email 用户邮箱 例如 <EMAIL>
 * @param code 验证码
 */
const updateUserInfo = ({ user_id, name, avatar }: any) => {
  let url = `${url_nest}/update_user`;
  return post(url, { user_id, name, avatar });
};

/**
 * 重置密码
 * @param email 用户邮箱 例如 <EMAIL>
 * @param password 用户密码
 * @param code 验证码
 */
const resetPassword = ({ email, password, code }: any) => {
  let url = `${url_nest}/reset_password`;
  return post(url, { email, password, code });
};

const checkout = () => {
  let url = `${url_nest}/checkout`;
  return post(url);
};

const createPlan = ({ cloth }: any) => {
  let url = `${url_nest}/ai_generate`;
  return post(url, { cloth });
};

export {
  register,
  send_verify_code,
  verify_email,
  login,
  getUserInfo,
  googleLogin,
  updateUserInfo,
  resetPassword,
  checkout,
  createPlan,
};
