// fetch 封装

const BASE_URL = "https://api.dreamloom.art";

type RequestHeaders = HeadersInit | Headers | undefined;
/// 使用示例
/// request("http://localhost:3000/api/user", "POST", { name: "<PERSON>" })
///   .then((res) => {
///     console.log(res);
///   })
///   .catch((err) => {
///     console.error(err);
///   });
///
/// request("http://localhost:3000/api/user", "GET")
///   .then((res) => {
///     console.log(res);
///   })
///   .catch((err) => {
///     console.error(err);
///   });
export default function request<T>(
  url: string,
  method: string,
  data: any = {}
): Promise<T> {
  let _url = url.includes("http://") ? url : `${BASE_URL}${url}`;
  const headers: RequestHeaders = {
    "Content-Type": "application/json",
  };
  const token = localStorage.getItem("token");
  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }
  return new Promise((resolve, reject) => {
    fetch(_url, {
      method,
      headers: headers,
      body: JSON.stringify(data),
    })
      .then((response) => response.json())
      .then((res) => {
        resolve(res);
      });
  });
}

export const post = <T>(url: string, data: any = {}): Promise<T> => {
  return request<T>(url, "POST", data);
};

export const get = <T>(url: string): Promise<T> => {
  return request<T>(url, "GET");
};

let cancelToken: any = null;
export const cancelRequest = () => {
  cancelToken && cancelToken();
};
export const post_with_progress = <T>(
  url: string,
  data: any = {},
  onProgress: (e: ProgressEvent) => void
): Promise<T> => {
  let xhr = new XMLHttpRequest();
  cancelToken = () => {
    xhr.abort(); // 防止重复调用
    cancelToken = null;
  };

  return new Promise((resolve, reject) => {
    xhr.upload.onprogress = onProgress;
    xhr.open("POST", `${BASE_URL}${url}`);
    xhr.setRequestHeader("Content-Type", "application/json");
    xhr.onload = function () {
      if (xhr.status === 200) {
        resolve(JSON.parse(xhr.responseText));
      } else {
        reject(xhr.statusText);
      }
    };
    xhr.onerror = function () {
      reject(xhr.statusText);
    };
    xhr.send(JSON.stringify(data));
  });
};
