import { post, post_with_progress } from "./request";

/**
 * 主要列表
 */
const getHomeData = () => {
  let url = `/homepage/videos`;
  return post(url);
};

/**
 * 查询计划列表
 * @param user_id 用户id
 * @param page_num 页码
 * @param page_size 每页条数
 */
const getPlanList = (
  user_id: string | null = "123456",
  page_num = 1,
  page_size = 10
) => {
  let url = `/project/plan/list`;
  let data = {
    user_id: user_id || "123456",
    page_num,
    page_size,
  };
  return post(url, data);
};

/**
 * 查询计划详情
 * @param plan_id 计划id 例如 2i9xgoEAs2Q
 */
const getPlanDetail = (plan_id: string) => {
  let url = "/project/plan/query";
  return post(url, { plan_id });
};

/**
 * 上传图片
 * @param {Object} data
 * @param {string} data.name test001.jpg
 * @param {string} data.base64 data:image/jpeg;base64,/9j/xxxxxxxx
 */
const uploadImage = (data: any) => {
  let url = `/asset/image/upload`;
  return post(url, data);
};

const uploadImageWithProcess = (data: any, onProcess: (e: any) => void) => {
  let url = `/asset/image/upload`;
  return post_with_progress(url, data, onProcess);
};

/**
 * 创建计划
 * @param {Object} data
 * @param {string} data.name plan_test001
 * @param {string} data.user_id 123456
 * @param {Object} data.images
 * @param {string} data.images.cloth https://aimkt.obs.cn-north-4.myhuaweicloud.com/assets/testing/dress003_polly_pink.jpg
 */
const createPlan = (data: any) => {
  let url = `/project/plan/create`;
  return post(url, data);
};

/**
 * 修改计划
 * @param {Object} data
 * @param {string} data.plan_id 2i9xgoEAs2Q
 * @param {string} data.user_id 123456
 * @param {string} data.name    plan_test002
 */
const updatePlan = (data: any) => {
  let url = "/project/plan/edit";
  return post(url, data);
};

export {
  getHomeData,
  getPlanList,
  getPlanDetail,
  uploadImage,
  uploadImageWithProcess,
  createPlan,
  updatePlan,
};
