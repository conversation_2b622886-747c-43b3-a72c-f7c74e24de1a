import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  output: "export",
  reactStrictMode: false,
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // rewrites: async () => {
  //   return [{ source: "/user/", destination: "http://127.0.0.1:9000/" }];
  // },

  images: {
    unoptimized: true,
  },
  trailingSlash: true,
};

export default nextConfig;
